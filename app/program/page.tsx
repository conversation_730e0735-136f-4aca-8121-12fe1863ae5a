
import type { Metadata } from 'next'
"use client"
export const metadata: Metadata = {
  title: 'Program Pendidikan',
  description: 'Program pendidikan unggulan PTQ Al Ihsan yang meliputi tahfidz Al-Qur\'an, kajian kitab kuning, pendidikan karakter islami, dan public speaking untuk membentuk generasi Qur\'ani.',
  keywords: [
    'program tahfidz',
    'program pendidikan islam',
    'kajian kitab kuning',
    'pendidikan karakter islami',
    'public speaking',
    'program pondok pesantren',
    'kurikulum tahfidz'
  ],
  openGraph: {
    title: 'Program Pendidikan - PTQ Al Ihsan',
    description: 'Program pendidikan unggulan PTQ Al Ihsan yang meliputi tahfidz Al-Qur\'an, kajian kitab kuning, pendidikan karakter islami, dan public speaking.',
    type: 'website',
  },
  alternates: {
    canonical: '/program',
  },
}


import type React from "react"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { BookOpen, BookText, Languages, Mic, Award, Shield, Loader2 } from "lucide-react"
import { useProgramSettings, useFeatureFlags } from "@/hooks/use-website-settings"

export default function ProgramPage() {
  // Load dynamic data from settings
  const programSettings = useProgramSettings()
  const featureFlags = useFeatureFlags()

  // Show loading state while data is being fetched
  if (programSettings.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-600 mx-auto" />
          <p className="mt-2 text-gray-600">Memuat halaman program...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6">Program Unggulan</h1>
            <p className="text-xl text-emerald-100">
              Kurikulum komprehensif yang dirancang untuk mengembangkan santri dalam ilmu agama dan keterampilan hidup.
            </p>
          </div>
        </div>
      </section>

      {/* Main Programs */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Program Utama</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Program-program unggulan yang menjadi fokus pendidikan di PTQ Al Ihsan.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <ProgramCard
              icon={<BookOpen className="h-12 w-12 text-emerald-600" />}
              title="Tahfidz Al-Qur'an"
              description="Program hafalan Al-Qur'an dengan target minimal 7 juz selama masa pendidikan. Santri dibimbing oleh asatidz yang berpengalaman dengan metode yang terpadu."
              features={[
                "Perbaikan dan pelancaran bacaan Al Qur'an",
                "Setoran hafalan baru harian",
                "Muroja'ah berkala",
                "Ujian hafalan per juz",
                "Sertifikasi hafalan bagi yang berhak",
              ]}
            />

            <ProgramCard
              icon={<Shield className="h-12 w-12 text-emerald-600" />}
              title="Kajian Aqidah"
              description="Pembelajaran dasar-dasar aqidah Islam melalui kitab Ushul Tsalatsah. Santri diajarkan pemahaman yang benar tentang tauhid dan aqidah Islam."
              features={[
                "Hafalan matan Ushul Tsalatsah",
                "Pemahaman tauhid yang benar",
                "Penerapan aqidah dalam kehidupan",
                "Ujian pemahaman dan hafalan matan berkala",
              ]}
            />

            <ProgramCard
              icon={<Award className="h-12 w-12 text-emerald-600" />}
              title="Kajian Hadits"
              description="Pembelajaran hadits-hadits pilihan dari kitab Hadits Arbain. Santri diajarkan untuk memahami dan mengamalkan hadits-hadits Nabi dalam kehidupan sehari-hari."
              features={[
                "Hafalan 42 hadits pilihan",
                "Pemahaman makna hadits",
                "Penerapan hadits dalam kehidupan",
                "Ujian hafalan dan pemahaman",
              ]}
            />

            <ProgramCard
              icon={<BookText className="h-12 w-12 text-emerald-600" />}
              title="Kajian Fiqih"
              description="Pembelajaran fiqih Islam melalui kitab Bidayatul Mutafaqqih. Santri diajarkan pemahaman tentang hukum-hukum Islam dalam ibadah dan muamalah."
              features={[
                "Hafalan Matan Bidayatul Mutafaqqih Bab Thaharah s.d Sholat",
                "Pemahaman makna Matan Bidayatul Mutafaqqih",
                "Penerapan Fiqih dalam kehidupan",
                "Ujian hafalan dan pemahaman",
              ]}
            />

            <ProgramCard
              icon={<Languages className="h-12 w-12 text-emerald-600" />}
              title="Kajian Bahasa Arab"
              description="Pembelajaran dasar-dasar bahasa Arab melalui kitab Al Ajurumiyah. Santri diajarkan tata bahasa Arab untuk memahami Al-Qur'an dan hadits."
              features={[
                "Hafalan matan Al Ajurumiyah",
                "Pemahaman nahwu dan sharaf",
                "Praktik membaca kitab gundul",
                "Ujian Hafalan dan Kemampuan membaca kitab gundul",
              ]}
            />

            <ProgramCard
              icon={<Mic className="h-12 w-12 text-emerald-600" />}
              title="Public Speaking"
              description="Pelatihan kemampuan berbicara di depan umum, seperti pidato, khutbah, dan presentasi. Santri dilatih untuk menjadi Leader yang mampu menyampaikan informasi dengan baik."
              features={[
                "Teknik pidato dan khutbah",
                "Praktik kultum harian",
                "Lomba pidato internal",
                "Tampil di acara-acara pondok",
              ]}
            />

            <ProgramCard
              icon={<BookOpen className="h-12 w-12 text-emerald-600" />}
              title="Mapel Umum"
              description="Pembekalan mata Pelajaran umum yang efektif pada bab pilihan sesuai kebutuhan yang digunakan pada kehidupan, dan materi tambahan penunjang wawasan global."
              features={[
                "Matematika & Bahasa Indonesia (Wajib)",
                "Bahasa Inggris (dianjurkan)",
                "PKN, IPA, IPS (tambahan)",
              ]}
            />
          </div>
        </div>
      </section>

      {/* Extracurricular */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Ekstrakurikuler</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Kegiatan tambahan untuk mengembangkan bakat dan Pribadi santri.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow border border-gray-100">
              <h3 className="text-xl font-bold text-emerald-700 mb-4">SETIAP PEKAN</h3>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Futsal
                </li>
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Jiujutsu
                </li>
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Berenang
                </li>
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Public Speaking
                </li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow border border-gray-100">
              <h3 className="text-xl font-bold text-emerald-700 mb-4">SETIAP 2 BULAN</h3>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Entrepreneurship
                </li>
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Leadership
                </li>
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Kegiatan Sosial Masyarakat
                </li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow border border-gray-100">
              <h3 className="text-xl font-bold text-emerald-700 mb-4">SETIAP SEMESTER</h3>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Camping
                </li>
                <li className="flex items-center gap-2 text-gray-700">
                  <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
                  Kunjungan Industri
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Daily Schedule */}
      <section className="py-16 bg-emerald-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Jadwal Harian Santri</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Aktivitas santri yang terstruktur untuk memaksimalkan proses pembelajaran.
            </p>
          </div>

          <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
            <table className="w-full">
              <thead className="bg-emerald-700 text-white">
                <tr>
                  <th className="py-3 px-4 text-left">Waktu</th>
                  <th className="py-3 px-4 text-left">Kegiatan</th>
                </tr>
              </thead>
              <tbody>
                <ScheduleRow time="03:30 - 04:30" activity="Bangun tidur, Shalat Tahajud" />
                <ScheduleRow time="04:30 - 05:30" activity="Shalat Subuh berjamaah, Dzikir pagi" />
                <ScheduleRow time="05:30 - 06:30" activity="Setoran hafalan Al-Qur'an" />
                <ScheduleRow time="06:30 - 07:30" activity="Sarapan dan persiapan belajar" />
                <ScheduleRow time="07:30 - 12:00" activity="Pembelajaran formal" />
                <ScheduleRow time="12:00 - 13:00" activity="Shalat Dzuhur berjamaah, makan siang" />
                <ScheduleRow time="13:00 - 15:00" activity="Pembelajaran kitab" />
                <ScheduleRow time="15:00 - 16:00" activity="Shalat Ashar berjamaah, Dzikir petang" />
                <ScheduleRow time="16:00 - 17:30" activity="Muroja'ah hafalan, olahraga" />
                <ScheduleRow time="17:30 - 18:30" activity="Makan malam, persiapan Maghrib" />
                <ScheduleRow time="18:30 - 19:30" activity="Shalat Maghrib berjamaah, Dzikir" />
                <ScheduleRow time="19:30 - 20:30" activity="Shalat Isya berjamaah" />
                <ScheduleRow time="20:30 - 22:00" activity="Belajar mandiri, persiapan hafalan" />
                <ScheduleRow time="22:00" activity="Istirahat malam" />
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-emerald-700 to-emerald-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Tertarik dengan Program Kami?</h2>
          <p className="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
            Daftarkan putra/putri Anda sekarang dan berikan mereka pendidikan terbaik untuk masa depan yang cerah.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {featureFlags.ppdbMenu && (
              <Button asChild size="lg" className="bg-white text-emerald-800 hover:bg-emerald-100">
                <Link href="/ppdb">Daftar Sekarang</Link>
              </Button>
            )}
            <Button
              asChild
              size="lg"
              variant="outline"
              className="text-white border-white/20 hover:bg-white/10 hover:text-white"
            >
              <Link href="/kontak">Hubungi Kami</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}

function ProgramCard({
  icon,
  title,
  description,
  features,
}: {
  icon: React.ReactNode
  title: string
  description: string
  features: string[]
}) {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow border border-gray-100 p-6">
      <div className="mb-4">{icon}</div>
      <h3 className="text-2xl font-bold text-gray-800 mb-3">{title}</h3>
      <p className="text-gray-600 mb-6">{description}</p>
      <ul className="space-y-2">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center gap-2 text-gray-700">
            <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
            {feature}
          </li>
        ))}
      </ul>
    </div>
  )
}

function ExtracurricularCard({ title, description, image }: { title: string; description: string; image: string }) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative h-48">
        <Image src={image || "/placeholder.svg"} alt={title} fill className="object-cover" />
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  )
}

function ScheduleRow({ time, activity }: { time: string; activity: string }) {
  return (
    <tr className="border-b border-gray-100 hover:bg-gray-50">
      <td className="py-3 px-4 font-medium">{time}</td>
      <td className="py-3 px-4">{activity}</td>
    </tr>
  )
}
