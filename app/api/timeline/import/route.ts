import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"
import * as XLSX from 'xlsx'

// POST - Import timeline data from Excel
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get teacher profile
    const { data: teacher, error: teacherError } = await supabase
      .from('teachers')
      .select('id')
      .eq('profile_id', session.user.id)
      .single()

    if (teacherError || !teacher) {
      return NextResponse.json(
        { error: "Teacher not found" },
        { status: 403 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const classId = formData.get('class_id') as string
    const month = formData.get('month') as string
    const year = formData.get('year') as string

    if (!file || !classId || !month || !year) {
      return NextResponse.json(
        { error: "Missing required fields: file, class_id, month, year" },
        { status: 400 }
      )
    }

    // Read Excel file
    const buffer = await file.arrayBuffer()
    const workbook = XLSX.read(buffer, { type: 'array' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    const data = XLSX.utils.sheet_to_json(worksheet)

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: "Excel file is empty or invalid" },
        { status: 400 }
      )
    }

    // Get students in the class
    const { data: classStudents, error: classError } = await supabase
      .from('class_students')
      .select(`
        student_id,
        students (
          id,
          student_id,
          name
        )
      `)
      .eq('class_id', classId)

    if (classError) {
      return NextResponse.json(
        { error: classError.message },
        { status: 500 }
      )
    }

    // Get all subjects
    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('id, name')

    if (subjectsError) {
      return NextResponse.json(
        { error: subjectsError.message },
        { status: 500 }
      )
    }

    // Create subject name to ID mapping
    const subjectMap = subjects?.reduce((acc, subject) => {
      acc[subject.name.toLowerCase()] = subject.id
      return acc
    }, {} as Record<string, string>) || {}

    // Create student name/ID to ID mapping
    const studentMap = classStudents?.reduce((acc, cs) => {
      const student = cs.students
      acc[student.name.toLowerCase()] = student.id
      acc[student.student_id.toLowerCase()] = student.id
      return acc
    }, {} as Record<string, string>) || {}

    const results = []
    const errors = []

    // Process each row in Excel
    for (let i = 0; i < data.length; i++) {
      const row = data[i] as any
      
      try {
        // Find student by name or student_id
        const studentName = row['Nama Siswa'] || row['nama_siswa'] || row['student_name']
        const studentId = row['ID Siswa'] || row['student_id'] || row['id_siswa']
        
        let foundStudentId = null
        if (studentName) {
          foundStudentId = studentMap[studentName.toLowerCase()]
        }
        if (!foundStudentId && studentId) {
          foundStudentId = studentMap[studentId.toLowerCase()]
        }

        if (!foundStudentId) {
          errors.push(`Row ${i + 2}: Student not found - ${studentName || studentId}`)
          continue
        }

        // Create or update timeline entry
        const { data: timelineEntry, error: entryError } = await supabase
          .from('timeline_entries')
          .upsert({
            student_id: foundStudentId,
            teacher_id: teacher.id,
            month,
            year: parseInt(year)
          }, {
            onConflict: 'student_id,month,year'
          })
          .select()
          .single()

        if (entryError) {
          errors.push(`Row ${i + 2}: Failed to create timeline entry - ${entryError.message}`)
          continue
        }

        // Delete existing timeline details and activities
        await supabase
          .from('timeline_details')
          .delete()
          .eq('timeline_id', timelineEntry.id)

        await supabase
          .from('timeline_activities')
          .delete()
          .eq('timeline_id', timelineEntry.id)

        // Process subject columns
        const subjectDetails = []
        for (const [key, value] of Object.entries(row)) {
          if (!value || key.toLowerCase().includes('nama') || key.toLowerCase().includes('id')) continue

          // Try to match subject name
          const subjectId = subjectMap[key.toLowerCase()]
          if (subjectId && value) {
            // Parse value - could be "A|Juz 1|Good progress" format
            const parts = String(value).split('|')
            const grade = parts[0]?.trim() || ''
            const achievement = parts[1]?.trim() || ''
            const notes = parts[2]?.trim() || ''

            subjectDetails.push({
              timeline_id: timelineEntry.id,
              subject_id: subjectId,
              value: achievement,
              grade: grade,
              notes: notes
            })
          }
        }

        // Insert subject details
        if (subjectDetails.length > 0) {
          const { error: detailsError } = await supabase
            .from('timeline_details')
            .insert(subjectDetails)

          if (detailsError) {
            errors.push(`Row ${i + 2}: Failed to insert subject details - ${detailsError.message}`)
          }
        }

        // Process activities
        const activities = row['Aktivitas'] || row['activities'] || row['kegiatan']
        if (activities) {
          const activityList = String(activities).split(',').map(a => a.trim()).filter(a => a)
          const activityInserts = activityList.map(activity => ({
            timeline_id: timelineEntry.id,
            activity
          }))

          if (activityInserts.length > 0) {
            await supabase
              .from('timeline_activities')
              .insert(activityInserts)
          }
        }

        // Process behavior
        const behavior = row['Perilaku'] || row['behavior'] || row['akhlak']
        const behaviorNotes = row['Catatan Perilaku'] || row['behavior_notes'] || row['catatan_akhlak']
        
        if (behavior) {
          await supabase
            .from('behavior_records')
            .upsert({
              student_id: foundStudentId,
              teacher_id: teacher.id,
              month,
              year: parseInt(year),
              behavior_grade: behavior,
              notes: behaviorNotes || ''
            }, {
              onConflict: 'student_id,month,year'
            })
        }

        results.push({
          student_id: foundStudentId,
          student_name: studentName,
          timeline_entry_id: timelineEntry.id,
          status: 'success'
        })

      } catch (error: any) {
        errors.push(`Row ${i + 2}: ${error.message}`)
      }
    }

    return NextResponse.json({ 
      message: "Import completed",
      results,
      errors,
      summary: {
        total_rows: data.length,
        successful: results.length,
        failed: errors.length
      }
    })

  } catch (error: any) {
    console.error('Error in timeline import:', error)
    return NextResponse.json(
      { error: error.message || "Failed to import timeline data" },
      { status: 500 }
    )
  }
}
