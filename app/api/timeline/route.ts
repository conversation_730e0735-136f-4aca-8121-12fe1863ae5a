import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Fetch timeline data for a specific class, month, and year
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('class_id')
    const month = searchParams.get('month')
    const year = searchParams.get('year')

    if (!classId || !month || !year) {
      return NextResponse.json(
        { error: "class_id, month, and year are required" },
        { status: 400 }
      )
    }

    // Get students in the class
    const { data: classStudents, error: classError } = await supabase
      .from('class_students')
      .select(`
        student_id,
        students (
          id,
          student_id,
          name,
          photo_url
        )
      `)
      .eq('class_id', classId)

    if (classError) {
      console.error('Error fetching class students:', classError)
      return NextResponse.json(
        { error: classError.message },
        { status: 500 }
      )
    }

    // Get timeline entries for these students
    const studentIds = classStudents?.map(cs => cs.student_id) || []
    
    const { data: timelineEntries, error: timelineError } = await supabase
      .from('timeline_entries')
      .select(`
        id,
        student_id,
        month,
        year,
        timeline_details (
          id,
          subject_id,
          value,
          grade,
          notes,
          subjects (
            id,
            name,
            category
          )
        ),
        timeline_activities (
          id,
          activity
        )
      `)
      .in('student_id', studentIds)
      .eq('month', month)
      .eq('year', parseInt(year))

    if (timelineError) {
      console.error('Error fetching timeline entries:', timelineError)
      return NextResponse.json(
        { error: timelineError.message },
        { status: 500 }
      )
    }

    // Get behavior records
    const { data: behaviorRecords, error: behaviorError } = await supabase
      .from('behavior_records')
      .select('*')
      .in('student_id', studentIds)
      .eq('month', month)
      .eq('year', parseInt(year))

    if (behaviorError) {
      console.error('Error fetching behavior records:', behaviorError)
    }

    // Get attendance data for the month
    const startDate = new Date(parseInt(year), getMonthNumber(month) - 1, 1)
    const endDate = new Date(parseInt(year), getMonthNumber(month), 0)
    
    const { data: attendanceData, error: attendanceError } = await supabase
      .from('attendance')
      .select('student_id, status')
      .in('student_id', studentIds)
      .gte('attendance_date', startDate.toISOString().split('T')[0])
      .lte('attendance_date', endDate.toISOString().split('T')[0])

    if (attendanceError) {
      console.error('Error fetching attendance data:', attendanceError)
    }

    // Calculate attendance percentage for each student
    const attendanceStats = studentIds.map(studentId => {
      const studentAttendance = attendanceData?.filter(a => a.student_id === studentId) || []
      const totalDays = studentAttendance.length
      const presentDays = studentAttendance.filter(a => a.status === 'present').length
      const percentage = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0
      
      return {
        student_id: studentId,
        attendance_percentage: percentage
      }
    })

    // Combine all data
    const studentsWithTimeline = classStudents?.map(cs => {
      const student = cs.students
      const timeline = timelineEntries?.find(te => te.student_id === student.id)
      const behavior = behaviorRecords?.find(br => br.student_id === student.id)
      const attendance = attendanceStats.find(as => as.student_id === student.id)

      return {
        ...student,
        timeline_entry: timeline || null,
        behavior_record: behavior || null,
        attendance_percentage: attendance?.attendance_percentage || 0
      }
    }) || []

    return NextResponse.json({ 
      students: studentsWithTimeline,
      class_id: classId,
      month,
      year: parseInt(year)
    })

  } catch (error: any) {
    console.error('Error in timeline GET:', error)
    return NextResponse.json(
      { error: error.message || "Failed to fetch timeline data" },
      { status: 500 }
    )
  }
}

// POST - Create or update timeline data
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get teacher profile
    const { data: teacher, error: teacherError } = await supabase
      .from('teachers')
      .select('id')
      .eq('profile_id', session.user.id)
      .single()

    if (teacherError || !teacher) {
      return NextResponse.json(
        { error: "Teacher not found" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { class_id, month, year, timeline_data } = body

    if (!class_id || !month || !year || !timeline_data) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Process each student's timeline data
    const results = []
    
    for (const [studentId, data] of Object.entries(timeline_data as Record<string, any>)) {
      // Create or update timeline entry
      const { data: timelineEntry, error: entryError } = await supabase
        .from('timeline_entries')
        .upsert({
          student_id: studentId,
          teacher_id: teacher.id,
          month,
          year: parseInt(year)
        }, {
          onConflict: 'student_id,month,year'
        })
        .select()
        .single()

      if (entryError) {
        console.error('Error creating timeline entry:', entryError)
        continue
      }

      // Delete existing timeline details and activities
      await supabase
        .from('timeline_details')
        .delete()
        .eq('timeline_id', timelineEntry.id)

      await supabase
        .from('timeline_activities')
        .delete()
        .eq('timeline_id', timelineEntry.id)

      // Insert new timeline details for each subject
      if (data.subjects) {
        for (const [subjectId, subjectData] of Object.entries(data.subjects as Record<string, any>)) {
          if (subjectData.value || subjectData.grade || subjectData.notes) {
            await supabase
              .from('timeline_details')
              .insert({
                timeline_id: timelineEntry.id,
                subject_id: subjectId,
                value: subjectData.value || '',
                grade: subjectData.grade || '',
                notes: subjectData.notes || ''
              })
          }
        }
      }

      // Insert activities
      if (data.activities && Array.isArray(data.activities)) {
        const validActivities = data.activities.filter((activity: string) => activity.trim())
        for (const activity of validActivities) {
          await supabase
            .from('timeline_activities')
            .insert({
              timeline_id: timelineEntry.id,
              activity: activity.trim()
            })
        }
      }

      // Update behavior record
      if (data.behavior) {
        await supabase
          .from('behavior_records')
          .upsert({
            student_id: studentId,
            teacher_id: teacher.id,
            month,
            year: parseInt(year),
            behavior_grade: data.behavior,
            notes: data.behavior_notes || ''
          }, {
            onConflict: 'student_id,month,year'
          })
      }

      results.push({
        student_id: studentId,
        timeline_entry_id: timelineEntry.id,
        status: 'success'
      })
    }

    return NextResponse.json({ 
      message: "Timeline data saved successfully",
      results 
    })

  } catch (error: any) {
    console.error('Error in timeline POST:', error)
    return NextResponse.json(
      { error: error.message || "Failed to save timeline data" },
      { status: 500 }
    )
  }
}

// Helper function to convert month name to number
function getMonthNumber(monthName: string): number {
  const months = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ]
  return months.indexOf(monthName) + 1
}
