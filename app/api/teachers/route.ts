import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET teachers
export async function GET(request: Request) {
  const supabase = await createServerClient()
  
  // Check authentication
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    )
  }
  
  // Check if requesting a specific teacher by ID
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')
  
  try {
    if (id) {
      // Get single teacher
      const { data: teacher, error } = await supabase
        .from("teachers")
        .select(`
          id,
          teacher_id,
          name,
          specialization,
          photo_url,
          join_date,
          status,
          created_at,
          updated_at,
          profile_id,
          profiles (
            email,
            phone
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error("Error fetching single teacher:", error)
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        )
      }

      return NextResponse.json({ teacher })
    } else {
      // Get all teachers
      const { data: teachers, error } = await supabase
        .from("teachers")
        .select(`
          id,
          teacher_id,
          name,
          specialization,
          photo_url,
          join_date,
          status,
          created_at,
          updated_at
        `)
        .eq('status', 'active')
        .order("name", { ascending: true })

      if (error) {
        console.error("Error fetching teachers:", error)
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        )
      }

      return NextResponse.json({ teachers })
    }
  } catch (error: any) {
    console.error("Catch error in teachers API:", error)
    return NextResponse.json(
      { error: error.message || "Gagal mengambil data guru" },
      { status: 500 }
    )
  }
}

// CREATE a new teacher
export async function POST(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the teacher data from the request
    const teacherData = await request.json()
    
    // Insert into users table with role 'teacher'
    const { data, error } = await supabase
      .from('users')
      .insert({
        name: teacherData.name,
        email: teacherData.email,
        role: 'teacher',
      })
      .select()
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ teacher: data })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal membuat data guru" },
      { status: 500 }
    )
  }
}

// UPDATE an existing teacher
export async function PUT(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the teacher data from the request
    const { id, ...teacherData } = await request.json()
    
    if (!id) {
      return NextResponse.json(
        { error: "ID guru wajib diisi" },
        { status: 400 }
      )
    }
    
    // Update the teacher
    const { data, error } = await supabase
      .from('users')
      .update({
        name: teacherData.name,
        email: teacherData.email,
      })
      .eq('id', id)
      .eq('role', 'teacher')
      .select()
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ teacher: data })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal memperbarui data guru" },
      { status: 500 }
    )
  }
}

// DELETE a teacher
export async function DELETE(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the ID from the URL
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: "ID guru wajib diisi" },
        { status: 400 }
      )
    }
    
    // Delete the teacher (soft delete by changing role or hard delete)
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id)
      .eq('role', 'teacher')
    
    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal menghapus data guru" },
      { status: 500 }
    )
  }
}
