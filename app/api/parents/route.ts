import { createServerClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { createParent, updateParent, deleteParent } from "@/lib/supabase-crud"

// Define types for better type safety
type Parent = {
  id: string;
  user_id: string;
  name: string;
  phone: string;
  email: string;
  address: string;
  occupation: string;
}

// GET parents - this can be modified based on your access control needs
export async function GET(request: Request) {
  const supabase = await createServerClient()
  
  // Check authentication
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    )
  }
  
  // Get URL parameters
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  const id = searchParams.get('id')
  
  try {
    let query = supabase
      .from("parents")
      .select(`
        id,
        user_id,
        name,
        phone,
        email,
        address,
        occupation
      `)
    
    // Filter by ID if provided
    if (id) {
      query = query.eq('id', id)
    }
    
    // Filter by user ID if provided
    if (userId) {
      query = query.eq('user_id', userId)
    }
    
    // Handle single parent retrieval
    if (id || userId) {
      const { data: parent, error } = await query.maybeSingle()
      
      if (error) {
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        )
      }
      
      return NextResponse.json({ parent })
    }
    
    // Handle multiple parents retrieval
    const { data: parents, error } = await query
    
    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ parents })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal mendapatkan data orang tua" },
      { status: 500 }
    )
  }
}

// CREATE a new parent
export async function POST(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the parent data from the request
    const parentData = await request.json()
    
    // Create the parent - use current user's ID or create a temporary one
    const result = await createParent({
      name: parentData.name,
      email: parentData.email || '',
      phone: parentData.phone,
      address: parentData.address || '',
      occupation: parentData.occupation || '',
      user_id: parentData.user_id || session.user.id, // Use current user's ID
    })
    
    return NextResponse.json({ parent: result })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal membuat data orang tua" },
      { status: 500 }
    )
  }
}

// UPDATE an existing parent
export async function PUT(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the parent data from the request
    const { id, ...parentData } = await request.json()
    
    if (!id) {
      return NextResponse.json(
        { error: "ID orang tua wajib diisi" },
        { status: 400 }
      )
    }
    
    // Update the parent
    const result = await updateParent(id, parentData)
    
    return NextResponse.json(result)
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal memperbarui data orang tua" },
      { status: 500 }
    )
  }
}

// DELETE a parent
export async function DELETE(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the ID from the URL
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: "ID orang tua wajib diisi" },
        { status: 400 }
      )
    }
    
    // Delete the parent
    const result = await deleteParent(id)
    
    return NextResponse.json(result)
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal menghapus data orang tua" },
      { status: 500 }
    )
  }
}