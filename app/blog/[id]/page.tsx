import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Calendar, User, Tag, Share2, Facebook, Twitter, Linkedin, Copy } from "lucide-react"

// Sample blog posts data (same as in blog/page.tsx)
const blogPosts = [
  {
    id: "1",
    title: "Manfaat Menghafal Al-Qur'an Sejak Dini",
    excerpt:
      "Menghafal Al-Qur'an sejak dini memiliki banyak manfaat untuk perkembangan kognitif dan spiritual anak. Simak penjelasannya dalam artikel ini.",
    image: "/placeholder.svg?height=300&width=600",
    date: "15 Maret 2024",
    author: "Us<PERSON>z <PERSON>",
    category: "Pendidikan Islami",
    tags: ["tahfidz", "pendidikan anak", "al-quran"],
    content: `
      <p>Menghafal Al-Qur'an sejak dini memiliki banyak manfaat untuk perkembangan kognitif dan spiritual anak. <PERSON><PERSON>ut adalah beberapa manfaat menghafal Al-Qur'an sejak dini:</p>
      
      <h2>1. Meningkatkan Daya Ingat dan Konsentrasi</h2>
      <p>Menghafal Al-Qur'an melatih otak untuk menyimpan dan mengingat informasi dalam jumlah besar. Hal ini dapat meningkatkan daya ingat dan konsentrasi anak secara signifikan. Kemampuan ini akan sangat bermanfaat dalam proses belajar di sekolah dan kehidupan sehari-hari.</p>
      
      <h2>2. Membentuk Karakter dan Akhlak Mulia</h2>
      <p>Al-Qur'an mengandung ajaran-ajaran moral dan etika yang tinggi. Dengan menghafal Al-Qur'an, anak-anak tidak hanya menghafalkan kata-kata, tetapi juga menyerap nilai-nilai moral yang terkandung di dalamnya. Hal ini akan membantu membentuk karakter dan akhlak mulia pada anak.</p>
      
      <h2>3. Meningkatkan Kecerdasan Linguistik</h2>
      <p>Bahasa Al-Qur'an adalah bahasa Arab yang memiliki struktur dan kosa kata yang kaya. Menghafal Al-Qur'an dapat meningkatkan kecerdasan linguistik anak, termasuk kemampuan berbahasa, pemahaman tata bahasa, dan kosa kata.</p>
      
      <h2>4. Menanamkan Kedisiplinan dan Ketekunan</h2>
      <p>Proses menghafal Al-Qur'an membutuhkan kedisiplinan dan ketekunan. Anak-anak yang terbiasa menghafal Al-Qur'an akan belajar tentang pentingnya konsistensi, disiplin, dan kerja keras dalam mencapai tujuan.</p>
      
      <h2>5. Memberikan Ketenangan Jiwa</h2>
      <p>Al-Qur'an adalah obat bagi jiwa. Membaca dan menghafal Al-Qur'an dapat memberikan ketenangan jiwa dan mengurangi stres. Anak-anak yang terbiasa membaca dan menghafal Al-Qur'an cenderung lebih tenang dan mampu mengendalikan emosi dengan baik.</p>
      
      <h2>6. Menjadi Investasi Akhirat</h2>
      <p>Menghafal Al-Qur'an adalah investasi untuk kehidupan akhirat. Rasulullah SAW bersabda bahwa orang yang menghafal Al-Qur'an akan mendapatkan kedudukan yang tinggi di surga. Dengan mengajarkan anak menghafal Al-Qur'an, orang tua telah memberikan bekal terbaik untuk kehidupan akhirat anak.</p>
      
      <h2>Metode Efektif Mengajarkan Hafalan Al-Qur'an pada Anak</h2>
      <p>Berikut adalah beberapa metode efektif untuk mengajarkan hafalan Al-Qur'an pada anak:</p>
      
      <ol>
        <li>Mulai dari surat-surat pendek (Juz 'Amma)</li>
        <li>Gunakan metode pengulangan (tikrar)</li>
        <li>Jadikan proses menghafal menyenangkan dengan permainan</li>
        <li>Berikan reward untuk setiap pencapaian</li>
        <li>Jadikan menghafal Al-Qur'an sebagai rutinitas harian</li>
        <li>Gunakan teknologi seperti aplikasi atau video untuk membantu proses menghafal</li>
      </ol>
      
      <p>Di PTQ Al Ihsan, kami menerapkan metode-metode tersebut dalam program tahfidz untuk santri. Kami percaya bahwa setiap anak memiliki potensi untuk menjadi penghafal Al-Qur'an yang baik dengan bimbingan dan metode yang tepat.</p>
      
      <p>Jika Anda tertarik untuk mendaftarkan putra/putri Anda di program tahfidz PTQ Al Ihsan, silakan kunjungi halaman <a href="/ppdb">PPDB</a> atau hubungi kami melalui halaman <a href="/kontak">Kontak</a>.</p>
    `,
  },
  // Other blog posts data...
]

// Sample categories (same as in blog/page.tsx)
const categories = [
  { name: "Pendidikan Islami", count: 15 },
  { name: "Parenting", count: 8 },
  { name: "Kegiatan Pondok", count: 12 },
  { name: "Prestasi", count: 6 },
  { name: "Artikel Islami", count: 10 },
]

export default function BlogPostPage({ params }: { params: { id: string } }) {
  // Find the blog post by ID
  const post = blogPosts.find((post) => post.id === params.id)

  // If post not found, you might want to handle this case
  if (!post) {
    return <div>Post not found</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6">{post.title}</h1>
            <div className="flex flex-wrap justify-center gap-4 text-emerald-100">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{post.date}</span>
              </div>
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center gap-1">
                <Tag className="h-4 w-4" />
                <span>{post.category}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">
              <Button asChild variant="outline" className="mb-6">
                <Link href="/blog" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Kembali ke Blog
                </Link>
              </Button>

              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="relative h-80 w-full">
                  <Image src={post.image || "/placeholder.svg"} alt={post.title} fill className="object-cover" />
                </div>

                <div className="p-6 md:p-8">
                  {/* Share buttons */}
                  <div className="flex items-center gap-4 mb-6 pb-6 border-b">
                    <span className="text-gray-500 flex items-center gap-1">
                      <Share2 className="h-4 w-4" />
                      Bagikan:
                    </span>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Facebook className="h-4 w-4" />
                        <span className="sr-only">Share on Facebook</span>
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Twitter className="h-4 w-4" />
                        <span className="sr-only">Share on Twitter</span>
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Linkedin className="h-4 w-4" />
                        <span className="sr-only">Share on LinkedIn</span>
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Copy className="h-4 w-4" />
                        <span className="sr-only">Copy link</span>
                      </Button>
                    </div>
                  </div>

                  {/* Article content */}
                  <div className="prose prose-emerald max-w-none" dangerouslySetInnerHTML={{ __html: post.content }} />

                  {/* Tags */}
                  <div className="mt-8 pt-6 border-t">
                    <div className="flex flex-wrap items-center gap-2">
                      <span className="text-gray-500">Tags:</span>
                      {post.tags.map((tag: string) => (
                        <Link
                          key={tag}
                          href={`/blog/tag/${tag.toLowerCase().replace(/\s+/g, "-")}`}
                          className="bg-gray-100 hover:bg-emerald-100 text-gray-700 hover:text-emerald-700 px-3 py-1 rounded-full text-sm transition-colors"
                        >
                          {tag}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:w-1/3 space-y-8">
              {/* Author Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Tentang Penulis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    <div className="relative h-16 w-16 rounded-full overflow-hidden">
                      <Image
                        src="/placeholder.svg?height=64&width=64"
                        alt={post.author}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-bold">{post.author}</h3>
                      <p className="text-sm text-gray-500">Pengajar di PTQ Al Ihsan</p>
                    </div>
                  </div>
                  <p className="mt-4 text-gray-600">
                    Penulis adalah pengajar berpengalaman di PTQ Al Ihsan dengan fokus pada pendidikan tahfidz dan
                    pembentukan karakter Islami.
                  </p>
                </CardContent>
              </Card>

              {/* Categories */}
              <Card>
                <CardHeader>
                  <CardTitle>Kategori</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {categories.map((category) => (
                      <li key={category.name}>
                        <Link
                          href={`/blog/category/${category.name.toLowerCase().replace(/\s+/g, "-")}`}
                          className="flex justify-between items-center py-2 hover:text-emerald-600 transition-colors"
                        >
                          <span>{category.name}</span>
                          <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                            {category.count}
                          </span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* Related Posts */}
              <Card>
                <CardHeader>
                  <CardTitle>Artikel Terkait</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-4">
                    {blogPosts
                      .filter((p) => p.id !== post.id && p.category === post.category)
                      .slice(0, 3)
                      .map((relatedPost) => (
                        <li key={relatedPost.id} className="flex gap-3">
                          <div className="relative w-16 h-16 flex-shrink-0">
                            <Image
                              src={relatedPost.image || "/placeholder.svg"}
                              alt={relatedPost.title}
                              fill
                              className="object-cover rounded-md"
                            />
                          </div>
                          <div>
                            <Link
                              href={`/blog/${relatedPost.id}`}
                              className="font-medium hover:text-emerald-600 transition-colors line-clamp-2"
                            >
                              {relatedPost.title}
                            </Link>
                            <p className="text-xs text-gray-500 mt-1">{relatedPost.date}</p>
                          </div>
                        </li>
                      ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

