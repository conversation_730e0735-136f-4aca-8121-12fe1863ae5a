import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, Calendar, User, Tag, Search } from "lucide-react"
import { Input } from "@/components/ui/input"

// Sample blog posts data
const blogPosts = [
  {
    id: "1",
    title: "Manfaat Menghafal Al-Qur'an Sejak Dini",
    excerpt:
      "Menghafal Al-Qur'an sejak dini memiliki banyak manfaat untuk perkembangan kognitif dan spiritual anak. Simak penjelasannya dalam artikel ini.",
    image: "/placeholder.svg?height=300&width=600",
    date: "15 Maret 2024",
    author: "Us<PERSON><PERSON>",
    category: "Pendidikan Islami",
    tags: ["tahfidz", "pendidikan anak", "al-quran"],
  },
  {
    id: "2",
    title: "Tips Parenting Islami dalam Mendidik <PERSON>",
    excerpt:
      "Bagaimana mendidik anak sesuai dengan ajaran <PERSON>? Artikel ini memberikan tips praktis untuk orang tua dalam mendidik anak secara Islami.",
    image: "/placeholder.svg?height=300&width=600",
    date: "10 Maret 2024",
    author: "Ustadzah Fatimah",
    category: "Parenting",
    tags: ["parenting", "pendidikan anak", "islam"],
  },
  {
    id: "3",
    title: "Metode Efektif Mengajarkan Bahasa Arab pada Anak",
    excerpt:
      "Bahasa Arab adalah kunci untuk memahami Al-Qur'an dan hadits. Simak metode efektif untuk mengajarkan bahasa Arab pada anak dalam artikel ini.",
    image: "/placeholder.svg?height=300&width=600",
    date: "5 Maret 2024",
    author: "Ustadz Abdullah",
    category: "Pendidikan Islami",
    tags: ["bahasa arab", "pendidikan anak", "metode belajar"],
  },
  {
    id: "4",
    title: "Kegiatan Ramadhan di PTQ Al Ihsan",
    excerpt:
      "PTQ Al Ihsan mengadakan berbagai kegiatan selama bulan Ramadhan untuk meningkatkan keimanan dan ketakwaan santri. Simak informasi lengkapnya di sini.",
    image: "/placeholder.svg?height=300&width=600",
    date: "1 Maret 2024",
    author: "Admin PTQ Al Ihsan",
    category: "Kegiatan Pondok",
    tags: ["ramadhan", "kegiatan", "pondok"],
  },
  {
    id: "5",
    title: "Prestasi Santri PTQ Al Ihsan dalam Kompetisi Tahfidz Nasional",
    excerpt:
      "Santri PTQ Al Ihsan berhasil meraih juara dalam Kompetisi Tahfidz Nasional. Simak kisah inspiratif mereka dalam artikel ini.",
    image: "/placeholder.svg?height=300&width=600",
    date: "25 Februari 2024",
    author: "Admin PTQ Al Ihsan",
    category: "Prestasi",
    tags: ["tahfidz", "kompetisi", "prestasi"],
  },
  {
    id: "6",
    title: "Pentingnya Pendidikan Karakter dalam Pembentukan Generasi Qur'ani",
    excerpt:
      "Pendidikan karakter memiliki peran penting dalam membentuk generasi Qur'ani yang berakhlak mulia. Simak penjelasannya dalam artikel ini.",
    image: "/placeholder.svg?height=300&width=600",
    date: "20 Februari 2024",
    author: "Ustadz Muhammad Rizki",
    category: "Pendidikan Islami",
    tags: ["pendidikan karakter", "akhlak", "generasi qur'ani"],
  },
]

// Sample categories
const categories = [
  { name: "Pendidikan Islami", count: 15 },
  { name: "Parenting", count: 8 },
  { name: "Kegiatan Pondok", count: 12 },
  { name: "Prestasi", count: 6 },
  { name: "Artikel Islami", count: 10 },
]

// Sample popular tags
const popularTags = [
  "tahfidz",
  "pendidikan anak",
  "al-quran",
  "parenting",
  "islam",
  "bahasa arab",
  "metode belajar",
  "ramadhan",
  "kegiatan",
  "pondok",
  "kompetisi",
  "prestasi",
  "pendidikan karakter",
  "akhlak",
  "generasi qur'ani",
]

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6">Blog & Artikel</h1>
            <p className="text-xl text-emerald-100">
              Informasi, tips, dan artikel seputar pendidikan Islami dan kegiatan di PTQ Al Ihsan.
            </p>
          </div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {blogPosts.map((post) => (
                  <BlogCard key={post.id} post={post} />
                ))}
              </div>

              {/* Pagination */}
              <div className="mt-12 flex justify-center">
                <nav className="flex items-center gap-1">
                  <Button variant="outline" size="icon" disabled>
                    <span className="sr-only">Previous page</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m15 18-6-6 6-6"></path>
                    </svg>
                  </Button>
                  <Button variant="outline" size="sm" className="bg-emerald-600 text-white hover:bg-emerald-700">
                    1
                  </Button>
                  <Button variant="outline" size="sm">
                    2
                  </Button>
                  <Button variant="outline" size="sm">
                    3
                  </Button>
                  <Button variant="outline" size="icon">
                    <span className="sr-only">Next page</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m9 18 6-6-6-6"></path>
                    </svg>
                  </Button>
                </nav>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:w-1/3 space-y-8">
              {/* Search */}
              <Card>
                <CardHeader>
                  <CardTitle>Pencarian</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <Input placeholder="Cari artikel..." className="pl-10" />
                  </div>
                </CardContent>
              </Card>

              {/* Categories */}
              <Card>
                <CardHeader>
                  <CardTitle>Kategori</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {categories.map((category) => (
                      <li key={category.name}>
                        <Link
                          href={`/blog/category/${category.name.toLowerCase().replace(/\s+/g, "-")}`}
                          className="flex justify-between items-center py-2 hover:text-emerald-600 transition-colors"
                        >
                          <span>{category.name}</span>
                          <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                            {category.count}
                          </span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* Popular Tags */}
              <Card>
                <CardHeader>
                  <CardTitle>Tag Populer</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {popularTags.map((tag) => (
                      <Link
                        key={tag}
                        href={`/blog/tag/${tag.toLowerCase().replace(/\s+/g, "-")}`}
                        className="bg-gray-100 hover:bg-emerald-100 text-gray-700 hover:text-emerald-700 px-3 py-1 rounded-full text-sm transition-colors"
                      >
                        {tag}
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Posts */}
              <Card>
                <CardHeader>
                  <CardTitle>Artikel Terbaru</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-4">
                    {blogPosts.slice(0, 3).map((post) => (
                      <li key={post.id} className="flex gap-3">
                        <div className="relative w-16 h-16 flex-shrink-0">
                          <Image
                            src={post.image || "/placeholder.svg"}
                            alt={post.title}
                            fill
                            className="object-cover rounded-md"
                          />
                        </div>
                        <div>
                          <Link
                            href={`/blog/${post.id}`}
                            className="font-medium hover:text-emerald-600 transition-colors line-clamp-2"
                          >
                            {post.title}
                          </Link>
                          <p className="text-xs text-gray-500 mt-1">{post.date}</p>
                        </div>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

function BlogCard({ post }: { post: any }) {
  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image src={post.image || "/placeholder.svg"} alt={post.title} fill className="object-cover" />
      </div>
      <CardHeader>
        <div className="flex items-center gap-4 text-sm text-gray-500 mb-2">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>{post.date}</span>
          </div>
          <div className="flex items-center gap-1">
            <User className="h-4 w-4" />
            <span>{post.author}</span>
          </div>
        </div>
        <CardTitle className="line-clamp-2">
          <Link href={`/blog/${post.id}`} className="hover:text-emerald-600 transition-colors">
            {post.title}
          </Link>
        </CardTitle>
        <CardDescription className="line-clamp-3">{post.excerpt}</CardDescription>
      </CardHeader>
      <CardFooter className="flex justify-between items-center">
        <div className="flex items-center gap-1 text-sm text-gray-500">
          <Tag className="h-4 w-4" />
          <span>{post.category}</span>
        </div>
        <Button asChild variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700 p-0">
          <Link href={`/blog/${post.id}`} className="flex items-center gap-1">
            Baca Selengkapnya
            <ArrowRight className="h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}

