"use client"

import type React from "react"
import {
  Side<PERSON>,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { GraduationCap, Home, User, BookOpen, Settings, LogOut, FileText, Users, School, ChevronDown, UserCheck, Trophy, Calendar } from "lucide-react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { createBrowserClient } from "@/utils/supabase/client"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const router = useRouter()
  const supabase = createBrowserClient()
  
  // Handle logout
  const handleLogout = async () => {
    try {
      await supabase.auth.signOut()
      router.push("/login")
    } catch (error) {
      console.error("Error signing out:", error)
      router.push("/login")
    }
  }

  return (
    <SidebarProvider>
      <div className="flex flex-1">
        <Sidebar>
          <SidebarHeader>
            <div className="flex items-center gap-2 px-4 py-2">
              <GraduationCap className="h-6 w-6 text-emerald-600" />
              <div>
                <h3 className="font-bold">PTQ Al Ihsan</h3>
                <p className="text-xs text-muted-foreground">Portal Admin</p>
              </div>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Menu Utama</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname === '/dashboard' ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Home className="mr-3 h-5 w-5" />
                        <span>Dashboard</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/students"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/students') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <User className="mr-3 h-5 w-5" />
                        <span>Data Santri</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/parents"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/parents') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Users className="mr-3 h-5 w-5" />
                        <span>Data Orang Tua</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/classes"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/classes') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <School className="mr-3 h-5 w-5" />
                        <span>Data Kelas</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/teachers"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/teachers') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <UserCheck className="mr-3 h-5 w-5" />
                        <span>Data Guru</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/subjects"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/subjects') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <BookOpen className="mr-3 h-5 w-5" />
                        <span>Mata Pelajaran</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/achievements"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/achievements') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Trophy className="mr-3 h-5 w-5" />
                        <span>Pencapaian</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/attendance"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/attendance') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Calendar className="mr-3 h-5 w-5" />
                        <span>Kehadiran</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarGroup>
              <SidebarGroupLabel>Administrasi</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/ppdb"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/ppdb') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <FileText className="mr-3 h-5 w-5" />
                        <span>Manajemen PPDB</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/users"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/users') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Users className="mr-3 h-5 w-5" />
                        <span>Pengguna</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            <SidebarGroup>
              <SidebarGroupLabel>Pengaturan</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                      <Link
                        href="/dashboard/settings"
                        className={`flex items-center px-4 py-2 rounded-md group ${
                          pathname.startsWith('/dashboard/settings') ? 'bg-emerald-50 text-emerald-700' : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Settings className="mr-3 h-5 w-5" />
                        <span>Pengaturan Akun</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  
                  <SidebarMenuItem>
                    <SidebarMenuButton 
                      onClick={handleLogout}
                      className="flex items-center w-full text-left px-4 py-2 rounded-md group text-gray-700 hover:bg-gray-100"
                    >
                      <LogOut className="mr-3 h-5 w-5" />
                      <span>Keluar</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
          <SidebarFooter>
            <div className="px-4 py-2 text-xs text-center text-muted-foreground">
              &copy; {new Date().getFullYear()} PTQ Al Ihsan
            </div>
          </SidebarFooter>
        </Sidebar>
        <main className="flex-grow p-4">{children}</main>
      </div>
    </SidebarProvider>
  )
}
