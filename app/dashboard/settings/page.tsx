"use client"

import { useState, useEffect } from "react"
import { Save, SettingsIcon, FileText, Users, BookOpen, MessageSquare, Globe, Mail, Image as ImageIcon, Loader2, Plus, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ImageUpload } from "@/components/image-upload"

interface WebsiteSetting {
  key: string
  value: string
  type: string
  category: string
  description?: string
}

interface WebsiteContent {
  section: string
  key: string
  title: string
  content: string
  order_index: number
  is_active: boolean
}

export default function SettingsPage() {
  // Loading states
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Data states
  const [settings, setSettings] = useState<{ [key: string]: WebsiteSetting }>({})
  const [content, setContent] = useState<{ [key: string]: WebsiteContent[] }>({})
  const [images, setImages] = useState<{ [key: string]: any }>({})

  // Load data on component mount
  useEffect(() => {
    loadAllData()
  }, [])

  const loadAllData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load settings
      const settingsResponse = await fetch('/api/website-settings')
      const settingsData = await settingsResponse.json()

      if (settingsResponse.ok) {
        const settingsMap = settingsData.settings.reduce((acc: any, setting: WebsiteSetting) => {
          acc[setting.key] = setting
          return acc
        }, {})
        setSettings(settingsMap)
      }

      // Load content
      const contentResponse = await fetch('/api/website-content')
      const contentData = await contentResponse.json()

      if (contentResponse.ok) {
        setContent(contentData.grouped || {})
      }

      // Load images
      const imagesResponse = await fetch('/api/upload')
      const imagesData = await imagesResponse.json()

      if (imagesResponse.ok) {
        const imagesMap = imagesData.images.reduce((acc: any, image: any) => {
          acc[image.key] = image
          return acc
        }, {})
        setImages(imagesMap)
      }

    } catch (error: any) {
      console.error('Error loading data:', error)
      setError('Gagal memuat data. Silakan refresh halaman.')
    } finally {
      setLoading(false)
    }
  }

  // Handle setting update
  const updateSetting = (key: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: { ...prev[key], value }
    }))
  }

  // Handle save settings
  const handleSaveSettings = async (category?: string) => {
    try {
      setSaving(true)
      setError(null)

      const settingsToSave = Object.values(settings).filter(setting =>
        !category || setting.category === category
      )

      const response = await fetch('/api/website-settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ settings: settingsToSave })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Gagal menyimpan pengaturan')
      }

      setSuccess('Pengaturan berhasil disimpan!')
      setTimeout(() => setSuccess(null), 3000)

    } catch (error: any) {
      console.error('Error saving settings:', error)
      setError(error.message || 'Gagal menyimpan pengaturan')
    } finally {
      setSaving(false)
    }
  }

  // Handle image upload success
  const handleImageUpload = (imageKey: string, imageData: any) => {
    setImages(prev => ({
      ...prev,
      [imageKey]: imageData.image
    }))
    setSuccess('Gambar berhasil diupload!')
    setTimeout(() => setSuccess(null), 3000)
  }

  if (loading) {
    return (
      <div className="container mx-auto p-4 md:p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Memuat pengaturan...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Pengaturan Website</h1>
        <p className="text-gray-600">Kelola fitur dan konten website PTQ Al Ihsan</p>
      </div>

      <Tabs defaultValue="features" className="w-full">
        <TabsList className="grid w-full md:w-auto grid-cols-4">
          <TabsTrigger value="features">Fitur Website</TabsTrigger>
          <TabsTrigger value="content">Konten</TabsTrigger>
          <TabsTrigger value="images">Gambar</TabsTrigger>
          <TabsTrigger value="contact">Kontak</TabsTrigger>
        </TabsList>

        <TabsContent value="features" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Pengaturan Fitur</CardTitle>
              <CardDescription>Aktifkan atau nonaktifkan fitur website</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Success/Error Messages */}
              {success && (
                <Alert>
                  <AlertDescription className="text-green-600">{success}</AlertDescription>
                </Alert>
              )}
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-emerald-100 p-2 rounded-full">
                    <FileText className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Menu PPDB</h3>
                    <p className="text-sm text-gray-500">Tampilkan menu pendaftaran santri baru</p>
                  </div>
                </div>
                <Switch
                  checked={settings['feature_ppdb_menu']?.value === 'true'}
                  onCheckedChange={(checked) => updateSetting('feature_ppdb_menu', checked.toString())}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-emerald-100 p-2 rounded-full">
                    <BookOpen className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Menu Blog</h3>
                    <p className="text-sm text-gray-500">Tampilkan menu blog dan artikel</p>
                  </div>
                </div>
                <Switch
                  checked={settings['feature_blog_menu']?.value === 'true'}
                  onCheckedChange={(checked) => updateSetting('feature_blog_menu', checked.toString())}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-emerald-100 p-2 rounded-full">
                    <MessageSquare className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Form Kontak</h3>
                    <p className="text-sm text-gray-500">Aktifkan formulir kontak di halaman kontak</p>
                  </div>
                </div>
                <Switch
                  checked={settings['feature_contact_form']?.value === 'true'}
                  onCheckedChange={(checked) => updateSetting('feature_contact_form', checked.toString())}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-emerald-100 p-2 rounded-full">
                    <Users className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Portal Orang Tua</h3>
                    <p className="text-sm text-gray-500">Aktifkan akses portal untuk orang tua santri</p>
                  </div>
                </div>
                <Switch
                  checked={settings['feature_parent_dashboard']?.value === 'true'}
                  onCheckedChange={(checked) => updateSetting('feature_parent_dashboard', checked.toString())}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-emerald-100 p-2 rounded-full">
                    <SettingsIcon className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Halaman Pencapaian</h3>
                    <p className="text-sm text-gray-500">Tampilkan halaman pencapaian santri</p>
                  </div>
                </div>
                <Switch
                  checked={settings['feature_achievement_page']?.value === 'true'}
                  onCheckedChange={(checked) => updateSetting('feature_achievement_page', checked.toString())}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => handleSaveSettings('features')}
                disabled={saving}
                className="gap-2"
              >
                {saving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                {saving ? 'Menyimpan...' : 'Simpan Pengaturan'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="mt-6">
          <div className="space-y-6">
            {/* Hero Section Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Hero Section</CardTitle>
                <CardDescription>Kelola konten utama halaman depan</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Hero Image Upload */}
                <ImageUpload
                  imageKey="hero_image"
                  category="hero"
                  currentImage={images['hero_image']?.file_path}
                  altText={images['hero_image']?.alt_text}
                  caption={images['hero_image']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('hero_image', data)}
                  label="Gambar Hero"
                  description="Upload gambar utama untuk hero section (recommended: 1200x600px)"
                />

                <div className="space-y-2">
                  <Label htmlFor="hero-title">Judul Hero Section</Label>
                  <Input
                    id="hero-title"
                    value={settings['hero_title']?.value || ''}
                    onChange={(e) => updateSetting('hero_title', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="hero-subtitle">Subjudul Hero Section</Label>
                  <Input
                    id="hero-subtitle"
                    value={settings['hero_subtitle']?.value || ''}
                    onChange={(e) => updateSetting('hero_subtitle', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="hero-description">Deskripsi Hero</Label>
                  <Textarea
                    id="hero-description"
                    value={settings['hero_description']?.value || ''}
                    onChange={(e) => updateSetting('hero_description', e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={() => handleSaveSettings('hero')}
                  disabled={saving}
                  className="gap-2"
                >
                  {saving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                  {saving ? 'Menyimpan...' : 'Simpan Hero Section'}
                </Button>
              </CardFooter>
            </Card>

            {/* Site Information */}
            <Card>
              <CardHeader>
                <CardTitle>Informasi Website</CardTitle>
                <CardDescription>Pengaturan dasar website</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="site-title">Judul Website</Label>
                  <Input
                    id="site-title"
                    value={settings['site_title']?.value || ''}
                    onChange={(e) => updateSetting('site_title', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="site-description">Deskripsi Website</Label>
                  <Textarea
                    id="site-description"
                    value={settings['site_description']?.value || ''}
                    onChange={(e) => updateSetting('site_description', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="site-keywords">Keywords SEO</Label>
                  <Input
                    id="site-keywords"
                    value={settings['site_keywords']?.value || ''}
                    onChange={(e) => updateSetting('site_keywords', e.target.value)}
                    placeholder="pondok tahfidz, pesantren, pendidikan islam"
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={() => handleSaveSettings('site_info')}
                  disabled={saving}
                  className="gap-2"
                >
                  {saving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                  {saving ? 'Menyimpan...' : 'Simpan Informasi Website'}
                </Button>
              </CardFooter>
            </Card>

            {/* PPDB Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Pengaturan PPDB</CardTitle>
                <CardDescription>Kelola informasi pendaftaran santri baru</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="ppdb-status">Status PPDB</Label>
                  <Select
                    value={settings['ppdb_status']?.value || 'open'}
                    onValueChange={(value) => updateSetting('ppdb_status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih status PPDB" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="open">Pendaftaran Dibuka</SelectItem>
                      <SelectItem value="closed">Pendaftaran Ditutup</SelectItem>
                      <SelectItem value="coming-soon">Segera Dibuka</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ppdb-batch">Batch Pendaftaran</Label>
                  <Input
                    id="ppdb-batch"
                    value={settings['ppdb_batch']?.value || ''}
                    onChange={(e) => updateSetting('ppdb_batch', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ppdb-date">Tanggal Pendaftaran</Label>
                  <Input
                    id="ppdb-date"
                    value={settings['ppdb_date']?.value || ''}
                    onChange={(e) => updateSetting('ppdb_date', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ppdb-announcement">Pengumuman PPDB</Label>
                  <Textarea
                    id="ppdb-announcement"
                    value={settings['ppdb_announcement']?.value || ''}
                    onChange={(e) => updateSetting('ppdb_announcement', e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={() => handleSaveSettings('ppdb')}
                  disabled={saving}
                  className="gap-2"
                >
                  {saving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                  {saving ? 'Menyimpan...' : 'Simpan Pengaturan PPDB'}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="contact" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Informasi Kontak</CardTitle>
              <CardDescription>Kelola informasi kontak yang ditampilkan di website</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="address">Alamat</Label>
                <Textarea
                  id="address"
                  value={settings['contact_address']?.value || ''}
                  onChange={(e) => updateSetting('contact_address', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone1">Nomor Telepon 1</Label>
                  <Input
                    id="phone1"
                    value={settings['contact_phone_1']?.value || ''}
                    onChange={(e) => updateSetting('contact_phone_1', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone2">Nomor Telepon 2</Label>
                  <Input
                    id="phone2"
                    value={settings['contact_phone_2']?.value || ''}
                    onChange={(e) => updateSetting('contact_phone_2', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email1">Email Utama</Label>
                  <Input
                    id="email1"
                    value={settings['contact_email_1']?.value || ''}
                    onChange={(e) => updateSetting('contact_email_1', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email2">Email PPDB</Label>
                  <Input
                    id="email2"
                    value={settings['contact_email_2']?.value || ''}
                    onChange={(e) => updateSetting('contact_email_2', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website URL</Label>
                <Input
                  id="website"
                  value={settings['contact_website']?.value || ''}
                  onChange={(e) => updateSetting('contact_website', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="operating-hours">Jam Operasional</Label>
                <Textarea
                  id="operating-hours"
                  value={settings['operating_hours']?.value || ''}
                  onChange={(e) => updateSetting('operating_hours', e.target.value)}
                />
              </div>

              <div className="space-y-4">
                <Label>Media Sosial</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="social-facebook">Facebook</Label>
                    <Input
                      id="social-facebook"
                      value={settings['social_facebook']?.value || ''}
                      onChange={(e) => updateSetting('social_facebook', e.target.value)}
                      placeholder="https://facebook.com/ptqalihsan"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="social-instagram">Instagram</Label>
                    <Input
                      id="social-instagram"
                      value={settings['social_instagram']?.value || ''}
                      onChange={(e) => updateSetting('social_instagram', e.target.value)}
                      placeholder="https://instagram.com/ptqalihsan"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="social-youtube">YouTube</Label>
                    <Input
                      id="social-youtube"
                      value={settings['social_youtube']?.value || ''}
                      onChange={(e) => updateSetting('social_youtube', e.target.value)}
                      placeholder="https://youtube.com/@ptqalihsan"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="social-whatsapp">WhatsApp</Label>
                    <Input
                      id="social-whatsapp"
                      value={settings['social_whatsapp']?.value || ''}
                      onChange={(e) => updateSetting('social_whatsapp', e.target.value)}
                      placeholder="https://wa.me/6282227374455"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => handleSaveSettings('contact')}
                disabled={saving}
                className="gap-2"
              >
                {saving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                {saving ? 'Menyimpan...' : 'Simpan Informasi Kontak'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="images" className="mt-6">
          <div className="space-y-6">
            {/* Program Images */}
            <Card>
              <CardHeader>
                <CardTitle>Gambar Program</CardTitle>
                <CardDescription>Upload gambar untuk program unggulan</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ImageUpload
                  imageKey="program_tahfidz"
                  category="programs"
                  currentImage={images['program_tahfidz']?.file_path}
                  altText={images['program_tahfidz']?.alt_text}
                  caption={images['program_tahfidz']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('program_tahfidz', data)}
                  label="Gambar Program Tahfidz"
                  description="Upload gambar untuk program Tahfidz Al-Qur'an"
                />

                <ImageUpload
                  imageKey="program_kitab"
                  category="programs"
                  currentImage={images['program_kitab']?.file_path}
                  altText={images['program_kitab']?.alt_text}
                  caption={images['program_kitab']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('program_kitab', data)}
                  label="Gambar Program Kitab Kuning"
                  description="Upload gambar untuk program Kajian Kitab Kuning"
                />

                <ImageUpload
                  imageKey="program_speaking"
                  category="programs"
                  currentImage={images['program_speaking']?.file_path}
                  altText={images['program_speaking']?.alt_text}
                  caption={images['program_speaking']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('program_speaking', data)}
                  label="Gambar Program Public Speaking"
                  description="Upload gambar untuk program Public Speaking"
                />
              </CardContent>
            </Card>

            {/* About Images */}
            <Card>
              <CardHeader>
                <CardTitle>Gambar Tentang Kami</CardTitle>
                <CardDescription>Upload gambar untuk halaman tentang kami</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ImageUpload
                  imageKey="about_hero"
                  category="about"
                  currentImage={images['about_hero']?.file_path}
                  altText={images['about_hero']?.alt_text}
                  caption={images['about_hero']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('about_hero', data)}
                  label="Gambar Hero Tentang Kami"
                  description="Upload gambar utama untuk halaman tentang kami"
                />

                <ImageUpload
                  imageKey="about_facility"
                  category="about"
                  currentImage={images['about_facility']?.file_path}
                  altText={images['about_facility']?.alt_text}
                  caption={images['about_facility']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('about_facility', data)}
                  label="Gambar Fasilitas"
                  description="Upload gambar fasilitas pesantren"
                />
              </CardContent>
            </Card>

            {/* Gallery Images */}
            <Card>
              <CardHeader>
                <CardTitle>Galeri</CardTitle>
                <CardDescription>Upload gambar untuk galeri website</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ImageUpload
                  imageKey="gallery_1"
                  category="gallery"
                  currentImage={images['gallery_1']?.file_path}
                  altText={images['gallery_1']?.alt_text}
                  caption={images['gallery_1']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('gallery_1', data)}
                  label="Gambar Galeri 1"
                  description="Upload gambar untuk galeri"
                />

                <ImageUpload
                  imageKey="gallery_2"
                  category="gallery"
                  currentImage={images['gallery_2']?.file_path}
                  altText={images['gallery_2']?.alt_text}
                  caption={images['gallery_2']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('gallery_2', data)}
                  label="Gambar Galeri 2"
                  description="Upload gambar untuk galeri"
                />

                <ImageUpload
                  imageKey="gallery_3"
                  category="gallery"
                  currentImage={images['gallery_3']?.file_path}
                  altText={images['gallery_3']?.alt_text}
                  caption={images['gallery_3']?.caption}
                  onUploadSuccess={(data) => handleImageUpload('gallery_3', data)}
                  label="Gambar Galeri 3"
                  description="Upload gambar untuk galeri"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
