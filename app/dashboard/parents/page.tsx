import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { PlusCircle } from "lucide-react"
import Link from "next/link"
import { ParentList } from "@/components/parents/parent-list"
import { createServerClient } from "@/utils/supabase/server"

// Function to get parents data from Supabase
async function getParents() {
  try {
    const supabase = await createServerClient()

    const { data: parents, error } = await supabase
      .from('parents')
      .select(`
        id,
        user_id,
        name,
        phone,
        email,
        address,
        occupation,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching parents:', error)
      throw error
    }

    return parents || []
  } catch (error) {
    console.error('Failed to get parents:', error)
    return []
  }
}

// Function to get parent-student relationships
async function getParentStudentCounts() {
  try {
    const supabase = await createServerClient()

    const { data: relations, error } = await supabase
      .from('student_parent')
      .select(`
        parent_id,
        students!inner(id, name)
      `)

    if (error) {
      console.error('Error fetching parent-student relations:', error)
      return {}
    }

    // Count students per parent
    const counts: Record<string, number> = {}
    relations?.forEach((relation) => {
      counts[relation.parent_id] = (counts[relation.parent_id] || 0) + 1
    })

    return counts
  } catch (error) {
    console.error('Failed to get parent-student counts:', error)
    return {}
  }
}

export default async function ParentsPage() {
  // Pre-fetch parents data from the server for initial render
  const [parents, studentCounts] = await Promise.all([
    getParents(),
    getParentStudentCounts()
  ])

  // Add student count to each parent
  const parentsWithCounts = parents.map(parent => ({
    ...parent,
    studentCount: studentCounts[parent.id] || 0
  }))

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Data Orangtua/Wali</h1>
          <p className="text-muted-foreground">Kelola data orangtua dan wali siswa</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/parents/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            Tambah Orangtua
          </Link>
        </Button>
      </div>
      <Separator className="my-6" />
      <Suspense fallback={<div>Loading...</div>}>
        <ParentList initialParents={parentsWithCounts} />
      </Suspense>
    </div>
  )
}
