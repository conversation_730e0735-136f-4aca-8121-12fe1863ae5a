"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Briefcase, 
  Users, 
  Edit, 
  Trash2, 
  Search,
  Eye
} from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"

type Parent = {
  id: string
  user_id: string
  name: string
  phone: string
  email: string
  address: string
  occupation: string
  created_at: string
  updated_at: string
  studentCount: number
}

interface ParentListProps {
  initialParents: Parent[]
}

export function ParentList({ initialParents }: ParentListProps) {
  const [parents, setParents] = useState<Parent[]>(initialParents)
  const [searchQuery, setSearchQuery] = useState("")
  const [loading, setLoading] = useState(false)

  // Filter parents based on search query
  const filteredParents = parents.filter(parent =>
    parent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    parent.phone.includes(searchQuery) ||
    parent.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    parent.occupation.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleDelete = async (parentId: string) => {
    if (!confirm("Apakah Anda yakin ingin menghapus data orangtua ini?")) {
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/parents?id=${parentId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setParents(prev => prev.filter(p => p.id !== parentId))
      } else {
        alert("Gagal menghapus data orangtua")
      }
    } catch (error) {
      console.error("Error deleting parent:", error)
      alert("Terjadi kesalahan saat menghapus data")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Cari orangtua berdasarkan nama, telepon, email, atau pekerjaan..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total Orangtua</p>
                <p className="text-2xl font-bold">{parents.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <User className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Dengan Siswa</p>
                <p className="text-2xl font-bold">{parents.filter(p => p.studentCount > 0).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Search className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Hasil Pencarian</p>
                <p className="text-2xl font-bold">{filteredParents.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Parent Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredParents.map((parent) => (
          <Card key={parent.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={`/placeholder.svg?height=48&width=48`} alt={parent.name} />
                    <AvatarFallback>
                      {parent.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{parent.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant={parent.studentCount > 0 ? "default" : "secondary"}>
                        {parent.studentCount} Siswa
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center text-sm text-muted-foreground">
                  <Phone className="h-4 w-4 mr-2" />
                  {parent.phone || "Tidak ada"}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Mail className="h-4 w-4 mr-2" />
                  {parent.email || "Tidak ada"}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Briefcase className="h-4 w-4 mr-2" />
                  {parent.occupation || "Tidak diketahui"}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 mr-2" />
                  {parent.address || "Tidak ada alamat"}
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                Dibuat {formatDistanceToNow(new Date(parent.created_at), {
                  addSuffix: true
                })}
              </div>

              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" asChild className="flex-1">
                  <Link href={`/dashboard/parents/${parent.id}`}>
                    <Eye className="h-4 w-4 mr-1" />
                    Detail
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="flex-1">
                  <Link href={`/dashboard/parents/${parent.id}/edit`}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Link>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleDelete(parent.id)}
                  disabled={loading}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredParents.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">Tidak ada orangtua ditemukan</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {searchQuery ? "Coba ubah kata kunci pencarian" : "Mulai dengan menambahkan orangtua baru"}
          </p>
        </div>
      )}
    </div>
  )
}
