"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Loader2, Save, UserCheck } from "lucide-react"

const teacherSchema = z.object({
  name: z.string().min(1, "Nama wajib diisi"),
  specialization: z.string().optional(),
})

type TeacherFormData = z.infer<typeof teacherSchema>

interface TeacherFormProps {
  mode: "create" | "edit"
  teacherId?: string
  initialData?: {
    id: string
    teacher_id: string
    name: string
    specialization: string
    status: string
  }
}

export function TeacherForm({ mode, teacherId, initialData }: TeacherFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [loadingData, setLoadingData] = useState(false)

  const form = useForm<TeacherFormData>({
    resolver: zodResolver(teacherSchema),
    defaultValues: {
      name: "",
      specialization: "",
    },
  })

  // Load teacher data for edit mode
  useEffect(() => {
    if (mode === "edit" && teacherId && !initialData) {
      const fetchTeacherData = async () => {
        setLoadingData(true)
        try {
          console.log("Fetching teacher data for:", teacherId)
          
          const response = await fetch(`/api/teachers?id=${teacherId}`)
          if (response.ok) {
            const data = await response.json()
            console.log("API response:", data)
            
            // Handle different response formats
            const teacher = data.teacher || data.teachers?.[0] || data
            
            console.log("Loaded teacher data:", teacher)
            
            if (teacher && teacher.name) {
              // Populate form with teacher data
              form.setValue("name", teacher.name || "")
              form.setValue("specialization", teacher.specialization || "")
            } else {
              console.error("Teacher data not found in response:", data)
            }
          } else {
            console.error("Failed to fetch teacher data:", response.status)
          }
        } catch (error) {
          console.error("Error fetching teacher data:", error)
        } finally {
          setLoadingData(false)
        }
      }

      fetchTeacherData()
    } else if (initialData) {
      // Use initial data if provided
      form.setValue("name", initialData.name || "")
      form.setValue("specialization", initialData.specialization || "")
    }
  }, [mode, teacherId, initialData, form])

  const onSubmit = async (data: TeacherFormData) => {
    setLoading(true)
    try {
      const url = mode === "create" ? "/api/teachers" : "/api/teachers"
      const method = mode === "create" ? "POST" : "PUT"
      
      const payload = mode === "edit" 
        ? { id: teacherId, ...data }
        : data

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        const result = await response.json()
        console.log("Teacher saved:", result)
        
        if (mode === "create") {
          router.push(`/dashboard/teachers/${result.teacher.id}`)
        } else {
          router.push(`/dashboard/teachers/${teacherId}`)
        }
      } else {
        const error = await response.json()
        console.error("Failed to save teacher:", error)
        alert(error.error || "Gagal menyimpan data guru")
      }
    } catch (error) {
      console.error("Error saving teacher:", error)
      alert("Terjadi kesalahan saat menyimpan data")
    } finally {
      setLoading(false)
    }
  }

  if (loadingData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Memuat data guru...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheck className="h-5 w-5" />
          {mode === "create" ? "Tambah Guru Baru" : "Edit Data Guru"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Lengkap</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan nama lengkap guru" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="specialization"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Spesialisasi</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Masukkan spesialisasi guru (opsional)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={loading}
              >
                Batal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {mode === "create" ? "Tambah Guru" : "Simpan Perubahan"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
