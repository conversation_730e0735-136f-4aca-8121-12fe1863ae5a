"use client"

import { useState, use<PERSON>ffe<PERSON> } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { CalendarIcon, Loader2, Trash2, User, UserPlus, Users } from "lucide-react"
import { format } from "date-fns"
import { id } from "date-fns/locale"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { useStudentCrud } from "@/hooks/use-student-crud"

// Types for API data
type Parent = {
  id: string;
  user_id: string;
  name: string;
  phone: string;
  email: string;
  address: string;
  occupation: string;
}

type Class = {
  id: string;
  class_id: string;
  name: string;
  level: string;
  academic_year: string;
}



// Schema validasi form siswa
const studentFormSchema = z.object({
  student_id: z.string().min(1, { message: "ID Siswa harus diisi" }),
  name: z.string().min(1, { message: "Nama harus diisi" }),
  gender: z.enum(["male", "female"], {
    required_error: "Jenis kelamin harus dipilih",
  }),
  birth_date: z.date({
    required_error: "Tanggal lahir harus diisi",
  }),
  address: z.string().min(1, { message: "Alamat harus diisi" }),
  batch: z.string().min(1, { message: "Angkatan harus diisi" }),
  class_id: z.string().min(1, { message: "Kelas harus dipilih" }),
  status: z.enum(["active", "inactive"], {
    required_error: "Status harus dipilih",
  }),
})

// Schema validasi form orangtua baru
const newParentFormSchema = z.object({
  name: z.string().min(1, { message: "Nama harus diisi" }),
  relationship: z.enum(["father", "mother", "guardian"], {
    required_error: "Hubungan harus dipilih",
  }),
  phone: z.string().min(1, { message: "Nomor telepon harus diisi" }),
  email: z.string().email({ message: "Format email tidak valid" }).optional().or(z.literal("")),
  address: z.string().min(1, { message: "Alamat harus diisi" }),
  occupation: z.string().optional(),
  is_primary: z.boolean().default(false),
})

// Tipe untuk data orangtua yang dipilih
type SelectedParent = {
  id: string
  name: string
  relationship: "father" | "mother" | "guardian"
  is_primary: boolean
}

export function StudentForm({ studentId }: { studentId?: string }) {
  const router = useRouter()
  const isEditMode = !!studentId
  const { createStudentWithParents, updateStudentWithParents, loading: crudLoading, error: crudError } = useStudentCrud()

  // State untuk data yang diambil dari API
  const [existingParents, setExistingParents] = useState<Parent[]>([])
  const [availableClasses, setAvailableClasses] = useState<Class[]>([])
  const [loadingData, setLoadingData] = useState(true)

  // State untuk orangtua yang dipilih
  const [selectedParents, setSelectedParents] = useState<SelectedParent[]>([])

  // State untuk dialog tambah orangtua baru
  const [newParentDialogOpen, setNewParentDialogOpen] = useState(false)

  // State untuk dialog pilih orangtua yang sudah ada
  const [existingParentDialogOpen, setExistingParentDialogOpen] = useState(false)

  // Fetch data parents dan classes saat komponen dimount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingData(true)

        // Fetch parents
        const parentsResponse = await fetch('/api/parents')
        if (parentsResponse.ok) {
          const parentsData = await parentsResponse.json()
          setExistingParents(parentsData.parents || [])
        }

        // Fetch classes
        const classesResponse = await fetch('/api/classes')
        if (classesResponse.ok) {
          const classesData = await classesResponse.json()
          setAvailableClasses(classesData.classes || [])
        }

        // Jika edit mode, fetch data student
        if (isEditMode && studentId) {
          console.log("Fetching student data for:", studentId)

          // Fetch student basic data
          const studentResponse = await fetch(`/api/students?id=${studentId}`)
          if (studentResponse.ok) {
            const studentData = await studentResponse.json()
            const student = studentData.student

            console.log("Loaded student data:", student)

            // Populate form dengan data student
            form.setValue("student_id", student.student_id || "")
            form.setValue("name", student.name || "")
            form.setValue("gender", student.gender || undefined)
            form.setValue("birth_date", student.birth_date ? new Date(student.birth_date) : undefined)
            form.setValue("address", student.address || "")
            form.setValue("batch", student.batch || "")
            form.setValue("status", student.status || "active")

            // Fetch student's class
            const classResponse = await fetch(`/api/students/${studentId}/classes`)
            if (classResponse.ok) {
              const classData = await classResponse.json()
              if (classData.classes && classData.classes.length > 0) {
                form.setValue("class_id", classData.classes[0].id)
              }
            }

            // Fetch student's parents
            const parentsResponse = await fetch(`/api/students/${studentId}/parents`)
            if (parentsResponse.ok) {
              const parentsData = await parentsResponse.json()
              if (parentsData.parents && parentsData.parents.length > 0) {
                const studentParents = parentsData.parents.map((parent: any) => ({
                  id: parent.id,
                  name: parent.name,
                  relationship: parent.relationship,
                  is_primary: parent.is_primary
                }))
                setSelectedParents(studentParents)
                console.log("Loaded student parents:", studentParents)
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Gagal mengambil data. Silakan refresh halaman.",
          variant: "destructive",
        })
      } finally {
        setLoadingData(false)
      }
    }

    fetchData()
  }, [isEditMode, studentId])

  // Form untuk siswa
  const form = useForm<z.infer<typeof studentFormSchema>>({
    resolver: zodResolver(studentFormSchema),
    defaultValues: {
      student_id: "",
      name: "",
      gender: undefined,
      birth_date: undefined,
      address: "",
      batch: "2024", // Use static value to avoid hydration mismatch
      class_id: "",
      status: "active",
    },
  })

  // Set current year after component mounts to avoid hydration issues
  useEffect(() => {
    if (!isEditMode) {
      form.setValue("batch", new Date().getFullYear().toString())
    }
  }, [form, isEditMode])

  // Form untuk orangtua baru
  const newParentForm = useForm<z.infer<typeof newParentFormSchema>>({
    resolver: zodResolver(newParentFormSchema),
    defaultValues: {
      name: "",
      relationship: undefined,
      phone: "",
      email: "",
      address: "",
      occupation: "",
      is_primary: false,
    },
  })

  // Handle submit form siswa
  const onSubmit = async (values: z.infer<typeof studentFormSchema>) => {
    try {
      // Validasi orangtua
      if (selectedParents.length === 0) {
        toast({
          title: "Error",
          description: "Siswa harus memiliki minimal satu orangtua/wali",
          variant: "destructive",
        })
        return
      }

      // Validasi orangtua utama
      const hasPrimaryParent = selectedParents.some((parent) => parent.is_primary)
      if (!hasPrimaryParent) {
        toast({
          title: "Error",
          description: "Siswa harus memiliki satu orangtua/wali utama",
          variant: "destructive",
        })
        return
      }

      // Siapkan data student
      const studentData = {
        student_id: values.student_id,
        name: values.name,
        gender: values.gender,
        birth_date: format(values.birth_date, 'yyyy-MM-dd'),
        address: values.address,
        batch: values.batch,
        status: values.status,
      }

      let result
      if (isEditMode && studentId) {
        // Update student
        result = await updateStudentWithParents(studentId, {
          studentData,
          classId: values.class_id,
          parents: selectedParents
        })
      } else {
        // Create new student with all selected parents
        result = await createStudentWithParents({
          studentData,
          classId: values.class_id,
          parents: selectedParents
        })
      }

      if (result) {
        toast({
          title: isEditMode ? "Data siswa berhasil diperbarui" : "Siswa baru berhasil ditambahkan",
          description: `${values.name} telah ${isEditMode ? "diperbarui" : "ditambahkan"} ke database.`,
        })

        // Redirect ke halaman daftar siswa
        router.push("/dashboard/students")
      }
    } catch (error) {
      console.error("Error submitting form:", error)
      toast({
        title: "Error",
        description: crudError || "Terjadi kesalahan saat menyimpan data. Silakan coba lagi.",
        variant: "destructive",
      })
    }
  }

  // Handle submit form orangtua baru
  const onNewParentSubmit = async (values: z.infer<typeof newParentFormSchema>) => {
    try {
      // Buat parent baru via API
      const response = await fetch('/api/parents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: values.name,
          phone: values.phone,
          email: values.email || '',
          address: values.address,
          occupation: values.occupation || '',
          // user_id will be generated on the server side
        }),
      })

      if (!response.ok) {
        throw new Error('Gagal membuat data orangtua')
      }

      const result = await response.json()
      const newParent = result.parent

      // Tambahkan orangtua baru ke daftar yang dipilih
      setSelectedParents((prev) => {
        // Jika orangtua baru ditandai sebagai utama, hapus tanda utama dari orangtua lain
        let updatedParents = prev
        if (values.is_primary) {
          updatedParents = prev.map((p) => ({
            ...p,
            is_primary: false,
          }))
        }

        return [
          ...updatedParents,
          {
            id: newParent.id,
            name: values.name,
            relationship: values.relationship,
            is_primary: values.is_primary,
          },
        ]
      })

      // Tambahkan ke daftar existing parents juga
      setExistingParents(prev => [...prev, newParent])

      // Reset form dan tutup dialog
      newParentForm.reset()
      setNewParentDialogOpen(false)

      toast({
        title: "Orangtua baru ditambahkan",
        description: `${values.name} telah ditambahkan sebagai ${
          values.relationship === "father" ? "Ayah" : values.relationship === "mother" ? "Ibu" : "Wali"
        }.`,
      })
    } catch (error) {
      console.error("Error creating parent:", error)
      toast({
        title: "Error",
        description: "Gagal membuat data orangtua. Silakan coba lagi.",
        variant: "destructive",
      })
    }
  }

  // Handle pilih orangtua yang sudah ada
  const handleSelectExistingParent = (
    parent: (typeof existingParents)[0],
    relationship: "father" | "mother" | "guardian",
  ) => {
    // Cek apakah orangtua sudah dipilih
    const isAlreadySelected = selectedParents.some((p) => p.id === parent.id)
    if (isAlreadySelected) {
      toast({
        title: "Orangtua sudah dipilih",
        description: `${parent.name} sudah ditambahkan ke daftar orangtua siswa ini.`,
        variant: "destructive",
      })
      return
    }

    // Tambahkan orangtua ke daftar yang dipilih
    setSelectedParents((prev) => [
      ...prev,
      {
        id: parent.id,
        name: parent.name,
        relationship,
        is_primary: prev.length === 0, // Jika belum ada orangtua, jadikan sebagai utama
      },
    ])

    // Tutup dialog
    setExistingParentDialogOpen(false)

    toast({
      title: "Orangtua ditambahkan",
      description: `${parent.name} telah ditambahkan sebagai ${
        relationship === "father" ? "Ayah" : relationship === "mother" ? "Ibu" : "Wali"
      }.`,
    })
  }

  // Handle hapus orangtua dari daftar
  const handleRemoveParent = (parentId: string) => {
    setSelectedParents((prev) => prev.filter((p) => p.id !== parentId))
  }

  // Handle set orangtua utama
  const handleSetPrimaryParent = (parentId: string) => {
    setSelectedParents((prev) =>
      prev.map((p) => ({
        ...p,
        is_primary: p.id === parentId,
      })),
    )
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Data Siswa</CardTitle>
              <CardDescription>Masukkan informasi dasar siswa</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="student_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ID Siswa</FormLabel>
                      <FormControl>
                        <Input placeholder="S0001" {...field} />
                      </FormControl>
                      <FormDescription>ID unik untuk siswa</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Lengkap</FormLabel>
                      <FormControl>
                        <Input placeholder="Nama lengkap siswa" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Jenis Kelamin</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih jenis kelamin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="male">Laki-laki</SelectItem>
                          <SelectItem value="female">Perempuan</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="birth_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Tanggal Lahir</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={`w-full pl-3 text-left font-normal ${
                                !field.value ? "text-muted-foreground" : ""
                              }`}
                              suppressHydrationWarning
                            >
                              {field.value ? (
                                format(field.value, "dd MMMM yyyy", { locale: id })
                              ) : (
                                <span>Pilih tanggal</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => {
                              const today = new Date()
                              const minDate = new Date("1900-01-01")
                              return date > today || date < minDate
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="batch"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Angkatan</FormLabel>
                      <FormControl>
                        <Input placeholder="2023" {...field} />
                      </FormControl>
                      <FormDescription>Tahun masuk siswa</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="class_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Kelas</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={loadingData}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={loadingData ? "Memuat..." : "Pilih kelas"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableClasses.map((cls) => (
                            <SelectItem key={cls.id} value={cls.id}>
                              {cls.name} - {cls.level}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Aktif</SelectItem>
                          <SelectItem value="inactive">Tidak Aktif</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alamat</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Alamat lengkap siswa" className="min-h-[80px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Data Orangtua/Wali</CardTitle>
              <CardDescription>Tambahkan orangtua atau wali siswa</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Dialog open={newParentDialogOpen} onOpenChange={setNewParentDialogOpen}>
                  <DialogTrigger asChild>
                    <Button type="button" variant="outline">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Tambah Orangtua Baru
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                      <DialogTitle>Tambah Orangtua Baru</DialogTitle>
                      <DialogDescription>
                        Masukkan data orangtua baru yang belum terdaftar dalam sistem
                      </DialogDescription>
                    </DialogHeader>

                    <Form {...newParentForm}>
                      <form onSubmit={newParentForm.handleSubmit(onNewParentSubmit)} className="space-y-4">
                        <FormField
                          control={newParentForm.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nama Lengkap</FormLabel>
                              <FormControl>
                                <Input placeholder="Nama lengkap orangtua" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={newParentForm.control}
                          name="relationship"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Hubungan</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Pilih hubungan" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="father">Ayah</SelectItem>
                                  <SelectItem value="mother">Ibu</SelectItem>
                                  <SelectItem value="guardian">Wali</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={newParentForm.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Nomor Telepon</FormLabel>
                                <FormControl>
                                  <Input placeholder="08xxxxxxxxxx" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={newParentForm.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Email</FormLabel>
                                <FormControl>
                                  <Input placeholder="<EMAIL>" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={newParentForm.control}
                          name="address"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Alamat</FormLabel>
                              <FormControl>
                                <Textarea placeholder="Alamat lengkap orangtua" className="min-h-[80px]" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={newParentForm.control}
                          name="occupation"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Pekerjaan</FormLabel>
                              <FormControl>
                                <Input placeholder="Pekerjaan orangtua" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={newParentForm.control}
                          name="is_primary"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <input
                                  type="checkbox"
                                  className="h-4 w-4 mt-1"
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Wali Utama</FormLabel>
                                <FormDescription>
                                  Jadikan sebagai wali utama yang akan dihubungi pertama kali
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />

                        <DialogFooter>
                          <Button type="button" variant="outline" onClick={() => setNewParentDialogOpen(false)}>
                            Batal
                          </Button>
                          <Button type="submit">Simpan</Button>
                        </DialogFooter>
                      </form>
                    </Form>
                  </DialogContent>
                </Dialog>

                <Dialog open={existingParentDialogOpen} onOpenChange={setExistingParentDialogOpen}>
                  <DialogTrigger asChild>
                    <Button type="button" variant="outline">
                      <User className="h-4 w-4 mr-2" />
                      Pilih Orangtua yang Sudah Ada
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                      <DialogTitle>Pilih Orangtua</DialogTitle>
                      <DialogDescription>Pilih orangtua yang sudah terdaftar dalam sistem</DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4 my-4">
                      <Input placeholder="Cari orangtua..." />

                      <div className="border rounded-md divide-y max-h-[300px] overflow-y-auto">
                        {existingParents.map((parent) => (
                          <div key={parent.id} className="p-3 flex justify-between items-center">
                            <div>
                              <p className="font-medium">{parent.name}</p>
                              <p className="text-sm text-muted-foreground">{parent.phone}</p>
                              <p className="text-sm text-muted-foreground">{parent.email}</p>
                            </div>
                            <div>
                              <Select
                                onValueChange={(value) =>
                                  handleSelectExistingParent(parent, value as "father" | "mother" | "guardian")
                                }
                              >
                                <SelectTrigger className="w-[140px]">
                                  <SelectValue placeholder="Hubungan" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="father">Ayah</SelectItem>
                                  <SelectItem value="mother">Ibu</SelectItem>
                                  <SelectItem value="guardian">Wali</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={() => setExistingParentDialogOpen(false)}>
                        Batal
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Orangtua/Wali Terpilih</h3>

                {selectedParents.length > 0 ? (
                  <div className="space-y-2">
                    {selectedParents.map((parent) => (
                      <div key={parent.id} className="flex items-center justify-between border rounded-md p-3">
                        <div className="flex items-center gap-2">
                          <User className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{parent.name}</p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>
                                {parent.relationship === "father"
                                  ? "Ayah"
                                  : parent.relationship === "mother"
                                    ? "Ibu"
                                    : "Wali"}
                              </span>
                              {parent.is_primary && (
                                <Badge variant="outline" className="text-xs">
                                  Wali Utama
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          {!parent.is_primary && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => handleSetPrimaryParent(parent.id)}
                            >
                              Jadikan Utama
                            </Button>
                          )}
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() => handleRemoveParent(parent.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-8 border rounded-lg">
                    <Users className="h-12 w-12 mx-auto text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-medium">Belum ada orangtua yang dipilih</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      Tambahkan orangtua baru atau pilih dari yang sudah ada
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="button" variant="outline" asChild>
                <a href="/dashboard/students">Batal</a>
              </Button>
              <Button type="submit" disabled={crudLoading || loadingData}>
                {crudLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditMode ? "Perbarui Siswa" : "Tambah Siswa"}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}
