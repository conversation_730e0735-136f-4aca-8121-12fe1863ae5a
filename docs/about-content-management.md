# Manajemen Konten Tentang Kami

## Overview
Fitur ini memungkinkan admin untuk mengedit konten halaman "Tentang Kami" melalui dashboard admin tanpa perlu mengubah kode secara langsung.

## Fitur yang Tersedia

### 1. Edit Konten Melalui Dashboard
- **Lokasi**: Dashboard → Settings → Tab "Tentang Kami"
- **Konten yang dapat diedit**:
  - **Visi**: Visi PTQ Al Ihsan
  - **Misi**: Misi PTQ Al Ihsan (mendukung multiple lines)
  - **Sejarah**: Sejarah singkat PTQ Al Ihsan

### 2. Penyimpanan Real-time
- Perubahan disimpan langsung ke database
- Notifikasi sukses/error setelah penyimpanan
- Data langsung terlihat di halaman publik

### 3. Format Konten
- **Visi**: Text area single paragraph
- **Misi**: Text area dengan dukungan multiple lines (pisahkan dengan Enter)
- **Sejarah**: Text area panjang untuk narasi sejarah

## Cara Menggunakan

### Mengakses Editor
1. Login sebagai admin
2. Buka Dashboard → Settings
3. Klik tab "Tentang Kami"

### Mengedit Konten
1. Edit konten di textarea yang tersedia
2. Untuk misi: pisahkan setiap poin dengan baris baru (Enter)
3. Klik tombol "Simpan Konten Tentang Kami"
4. Tunggu notifikasi sukses

### Melihat Hasil
1. Buka halaman `/tentang-kami` di website
2. Konten yang diubah akan langsung terlihat

## Struktur Database

### Tabel: website_content
```sql
- section: 'about'
- key: 'vision' | 'mission' | 'history'
- title: Judul konten
- content: Isi konten
- order_index: Urutan tampilan
- is_active: Status aktif/nonaktif
```

## API Endpoints

### GET /api/website-content
- Mengambil semua konten website
- Filter berdasarkan section: `?section=about`

### PUT /api/website-content
- Update multiple content items
- Body: `{ content: [array of content objects] }`

## File yang Terlibat

### Frontend
- `app/dashboard/settings/page.tsx` - Halaman settings dengan tab Tentang Kami
- `app/tentang-kami/page.tsx` - Halaman publik yang menampilkan konten
- `hooks/use-website-settings.ts` - Hook untuk mengambil data

### Backend
- `app/api/website-content/route.ts` - API untuk CRUD content
- `db/website_settings.sql` - Schema database

## Keamanan
- Hanya admin yang dapat mengakses editor
- RLS (Row Level Security) diterapkan di database
- Validasi input di API level

## Tips Penggunaan
1. **Misi**: Gunakan format poin-poin dengan memisahkan setiap misi dengan baris baru
2. **Sejarah**: Tulis dalam bentuk paragraf yang mengalir
3. **Visi**: Buat singkat dan jelas, maksimal 2-3 kalimat
4. **Preview**: Selalu cek hasil di halaman publik setelah menyimpan

## Troubleshooting

### Konten tidak tersimpan
- Pastikan koneksi internet stabil
- Cek console browser untuk error
- Pastikan login sebagai admin

### Konten tidak muncul di halaman publik
- Refresh halaman publik
- Cek apakah `is_active` = true di database
- Pastikan tidak ada error di console

### Format misi tidak sesuai
- Pastikan setiap poin misi dipisahkan dengan Enter
- Jangan gunakan bullet points manual (•, -, *)
- Sistem akan otomatis menambahkan icon checklist
