"use server"

import { createServerClient } from "@/utils/supabase/server"
import { Student, DatabaseStudent, Achievement, TimelineDetail } from "@/types"

// Types based on updated database schema
type DatabaseClass = {
  id: string
  class_id: string
  name: string
  level: string
  academic_year: string
  homeroom_teacher_id: string
}

type DatabaseParent = {
  id: string
  profile_id: string
  name: string
  phone: string
  email: string
  address: string
  occupation: string
  children?: any[] // Add children property for parent details
}

type DatabaseStudentParent = {
  id: string
  student_id: string
  parent_id: string
  relationship: string
  is_primary: boolean
}

type DatabaseClassStudent = {
  id: string
  class_id: string
  student_id: string
}

type DatabaseAchievement = {
  id: string
  student_id: string
  subject_id: string
  value: string
  grade: string
  notes: string
  verified_by: string
  achievement_date: string
  created_at: string
  updated_at: string
}

// ==================== STUDENTS ====================

export async function getStudents(limit = 100, offset = 0, search = "", classFilter = "", statusFilter = "active") {
  try {
    const supabase = await createServerClient()

    let query = supabase
      .from('students')
      .select(`
        id,
        student_id,
        name,
        gender,
        birth_date,
        address,
        photo_url,
        batch,
        status,
        created_at,
        updated_at
      `)
      .eq('status', statusFilter === "all" ? 'active' : statusFilter)
      .range(offset, offset + limit - 1)
      .order('name', { ascending: true })

    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    const { data: students, error } = await query

    if (error) {
      console.error('Error fetching students:', error)
      // Return mock data as fallback
      return getMockStudents()
    }
    
    // If filtering by class, we need to get class enrollments
    if (classFilter && classFilter !== "all") {
      const { data: classStudents, error: classError } = await supabase
        .from("class_students")
        .select("student_id")
        .eq("class_id", classFilter);
        
      if (classError) {
        console.error('Error fetching class students:', classError)
        return getMockStudents()
      }
      
      const studentIds = classStudents.map(cs => cs.student_id);
      students.filter(student => studentIds.includes(student.id));
    }
    
    // Get class information for students
    const studentIds = students.map(s => s.id);
    
    // Fetch classes for these students
    if (studentIds.length > 0) {
      const { data: classEnrollments, error: classError } = await supabase
        .from("class_students")
        .select(`
          student_id,
          class:classes (
            id,
            class_id,
            name,
            level,
            academic_year
          )
        `)
        .in("student_id", studentIds);
        
      if (!classError && classEnrollments) {
        // Create a map of student_id -> class
        const studentClasses = new Map();
        classEnrollments.forEach(enrollment => {
          studentClasses.set(enrollment.student_id, enrollment.class);
        });
        
        // Fetch parents for these students
        const { data: parentRelations, error: parentError } = await supabase
          .from("student_parent")
          .select(`
            student_id,
            parent:parents (
              id,
              name,
              email
            ),
            is_primary
          `)
          .in("student_id", studentIds)
          .eq("is_primary", true);

        const studentParents = new Map();
        if (!parentError && parentRelations) {
          parentRelations.forEach(relation => {
            studentParents.set(relation.student_id, relation.parent);
          });
        }

        // Fetch achievements for these students
        const { data: achievements, error: achievementsError } = await supabase
          .from("achievements")
          .select(`
            id,
            student_id,
            subject_id,
            value,
            grade,
            notes,
            verified_by,
            achievement_date,
            created_at,
            updated_at,
            subject:subjects (
              id,
              name,
              category
            )
          `)
          .in("student_id", studentIds)
          .order('achievement_date', { ascending: false });

        const studentAchievements = new Map();
        if (!achievementsError && achievements) {
          achievements.forEach(achievement => {
            if (!studentAchievements.has(achievement.student_id)) {
              studentAchievements.set(achievement.student_id, []);
            }
            studentAchievements.get(achievement.student_id).push(achievement);
          });
        }

        // Fetch latest timeline details for these students
        const { data: timelineDetails, error: timelineError } = await supabase
          .from("timeline_details")
          .select(`
            id,
            timeline_id,
            subject_id,
            value,
            grade,
            notes,
            created_at,
            updated_at,
            subject:subjects (
              id,
              name,
              category
            ),
            timeline_entry:timeline_entries (
              student_id,
              month,
              year
            )
          `)
          .order('created_at', { ascending: false });

        const studentTimelineDetails = new Map();
        if (!timelineError && timelineDetails) {
          timelineDetails.forEach(detail => {
            const studentId = detail.timeline_entry?.student_id;
            if (studentId && studentIds.includes(studentId)) {
              if (!studentTimelineDetails.has(studentId)) {
                studentTimelineDetails.set(studentId, []);
              }
              studentTimelineDetails.get(studentId).push(detail);
            }
          });
        }

        // Transform student data with all related information
        return students.map((student: any) => {
          return transformStudentDataWithAchievements(
            student,
            studentClasses.get(student.id),
            studentParents.get(student.id),
            studentAchievements.get(student.id) || [],
            studentTimelineDetails.get(student.id) || []
          );
        });
      }
    }
    
    return students.map(student => {
      // Create a student object with the fields we expect
      const typedStudent = {
        id: student.id,
        student_id: student.student_id,
        name: student.name,
        gender: student.gender,
        birth_date: student.birth_date,
        address: student.address,
        photo_url: student.photo_url,
        batch: student.batch,
        status: student.status
        // created_at and updated_at are not needed for transformStudentData
      } as Partial<DatabaseStudent> & Pick<DatabaseStudent, 'id' | 'student_id' | 'name'>;
      
      return transformStudentData(typedStudent as DatabaseStudent);
    });
  } catch (error) {
    console.error('Failed to get students:', error)
    // Return mock data as fallback
    return getMockStudents()
  }
}

export async function getStudentById(id: string) {
  try {
    const supabase = await createServerClient()
    
    // Get the student's basic information
    const { data: student, error } = await supabase
      .from('students')
      .select(`
        id,
        student_id,
        name,
        gender,
        birth_date,
        address,
        photo_url,
        batch,
        status
      `)
      .eq('id', id)
      .single()
    
    if (error) {
      console.error('Error fetching student:', error)
      // Return mock data as fallback
      return getMockStudentById(id)
    }
    
    // Get the student's class information
    const { data: classEnrollment, error: classError } = await supabase
      .from('class_students')
      .select(`
        class:classes (
          id,
          class_id,
          name,
          level,
          academic_year,
          homeroom_teacher:teachers (
            id,
            name
          )
        )
      `)
      .eq('student_id', id)
      .maybeSingle()
    
    // Get the student's parent information
    const { data: parentRelation, error: parentError } = await supabase
      .from('student_parent')
      .select(`
        parent:parents (
          id,
          name,
          email,
          phone,
          address,
          occupation
        ),
        relationship,
        is_primary
      `)
      .eq('student_id', id)
      .eq('is_primary', true)
      .maybeSingle()
    
    // Create a student object with the fields we expect
    const typedStudent = {
      id: student.id,
      student_id: student.student_id,
      name: student.name,
      gender: student.gender,
      birth_date: student.birth_date,
      address: student.address,
      photo_url: student.photo_url,
      batch: student.batch,
      status: student.status,
      profile_id: null // Students table doesn't have profile_id
      // created_at and updated_at are not needed for transformStudentData
    } as Partial<DatabaseStudent> & Pick<DatabaseStudent, 'id' | 'student_id' | 'name'>;
    
    return transformStudentData(
      typedStudent as DatabaseStudent,
      classEnrollment?.class,
      parentRelation?.parent,
      parentRelation?.relationship
    )
  } catch (error) {
    console.error('Failed to get student:', error)
    // Return mock data as fallback
    return getMockStudentById(id)
  }
}

export async function createStudent(studentData: Omit<DatabaseStudent, 'id' | 'created_at' | 'updated_at'>) {
  try {
    const supabase = await createServerClient()

    const { data, error } = await supabase
      .from('students')
      .insert(studentData)
      .select()
      .single()

    if (error) {
      console.error('Error creating student:', error)
      throw error
    }

    return { success: true, data }
  } catch (error) {
    console.error('Failed to create student:', error)
    throw error
  }
}

// Enhanced function to create student with relationships
// Enhanced function to create student with multiple parents
export async function createStudentWithParents(studentData: {
  studentData: {
    student_id: string;
    name: string;
    gender?: string;
    birth_date?: string;
    address?: string;
    batch?: string;
    status?: string;
  };
  classId?: string;
  parents: Array<{
    id: string;
    relationship: string;
    is_primary: boolean;
  }>;
}) {
  try {
    const supabase = await createServerClient()

    // Create the student first
    const { data: student, error: studentError } = await supabase
      .from('students')
      .insert({
        ...studentData.studentData,
        user_id: crypto.randomUUID(), // Generate temporary UUID, will be replaced when user account is created
      })
      .select()
      .single()

    if (studentError) {
      console.error('Error creating student:', studentError)
      throw studentError
    }

    console.log('Created student:', student)

    // Create class relationship if classId provided
    if (studentData.classId) {
      const { error: classError } = await supabase
        .from('class_students')
        .insert({
          class_id: studentData.classId,
          student_id: student.id,
        })

      if (classError) {
        console.error('Error creating class relationship:', classError)
        // Don't throw here, just log the error
      } else {
        console.log('Created class relationship for student:', student.id)
      }
    }

    // Create parent relationships for all selected parents
    if (studentData.parents && studentData.parents.length > 0) {
      const parentRelations = studentData.parents.map(parent => ({
        student_id: student.id,
        parent_id: parent.id,
        relationship: parent.relationship,
        is_primary: parent.is_primary,
      }))

      console.log('Creating parent relations:', parentRelations)

      const { error: parentError } = await supabase
        .from('student_parent')
        .insert(parentRelations)

      if (parentError) {
        console.error('Error creating parent relationships:', parentError)
        // Don't throw here, just log the error
      } else {
        console.log('Created parent relationships for student:', student.id)
      }
    }

    return { success: true, data: student }
  } catch (error) {
    console.error('Failed to create student with parents:', error)
    throw error
  }
}

// Legacy function for backward compatibility
export async function createStudentWithRelations(studentData: {
  studentData: {
    student_id: string;
    name: string;
    gender?: string;
    birth_date?: string;
    address?: string;
    batch?: string;
    status?: string;
  };
  classId?: string;
  parentId?: string;
  relationship?: string;
}) {
  try {
    const supabase = await createServerClient()

    // Create the student first
    const { data: student, error: studentError } = await supabase
      .from('students')
      .insert({
        ...studentData.studentData,
        user_id: crypto.randomUUID(), // Generate temporary UUID, will be replaced when user account is created
      })
      .select()
      .single()

    if (studentError) {
      console.error('Error creating student:', studentError)
      throw studentError
    }

    // Create class relationship if classId provided
    if (studentData.classId) {
      const { error: classError } = await supabase
        .from('class_students')
        .insert({
          class_id: studentData.classId,
          student_id: student.id,
        })

      if (classError) {
        console.error('Error creating class relationship:', classError)
        // Don't throw here, just log the error
      }
    }

    // Create parent relationship if parentId provided
    if (studentData.parentId) {
      const { error: parentError } = await supabase
        .from('student_parent')
        .insert({
          student_id: student.id,
          parent_id: studentData.parentId,
          relationship: studentData.relationship || 'parent',
          is_primary: true, // First parent is primary by default
        })

      if (parentError) {
        console.error('Error creating parent relationship:', parentError)
        // Don't throw here, just log the error
      }
    }

    return { success: true, data: student }
  } catch (error) {
    console.error('Failed to create student with relations:', error)
    throw error
  }
}

// Enhanced function to update student with multiple parents
export async function updateStudentWithParents(id: string, studentData: {
  studentData: {
    student_id?: string;
    name?: string;
    gender?: string;
    birth_date?: string;
    address?: string;
    batch?: string;
    status?: string;
  };
  classId?: string;
  parents?: Array<{
    id: string;
    relationship: string;
    is_primary: boolean;
  }>;
}) {
  try {
    const supabase = await createServerClient()

    // Update student basic data
    const { data: student, error: studentError } = await supabase
      .from('students')
      .update(studentData.studentData)
      .eq('id', id)
      .select()
      .single()

    if (studentError) {
      console.error('Error updating student:', studentError)
      throw studentError
    }

    console.log('Updated student:', student)

    // Update class relationship if classId provided
    if (studentData.classId) {
      // First, check if there's an existing enrollment
      const { data: existingEnrollment } = await supabase
        .from('class_students')
        .select('id')
        .eq('student_id', id)
        .maybeSingle()

      if (existingEnrollment) {
        // Update existing enrollment
        const { error: classError } = await supabase
          .from('class_students')
          .update({ class_id: studentData.classId })
          .eq('id', existingEnrollment.id)

        if (classError) {
          console.error('Error updating class relationship:', classError)
        } else {
          console.log('Updated class relationship for student:', id)
        }
      } else {
        // Create new enrollment
        const { error: classError } = await supabase
          .from('class_students')
          .insert({
            class_id: studentData.classId,
            student_id: id,
          })

        if (classError) {
          console.error('Error creating class relationship:', classError)
        } else {
          console.log('Created class relationship for student:', id)
        }
      }
    }

    // Update parent relationships if parents provided
    if (studentData.parents && studentData.parents.length > 0) {
      // First, delete existing parent relationships
      const { error: deleteError } = await supabase
        .from('student_parent')
        .delete()
        .eq('student_id', id)

      if (deleteError) {
        console.error('Error deleting existing parent relationships:', deleteError)
      } else {
        console.log('Deleted existing parent relationships for student:', id)
      }

      // Then, create new parent relationships
      const parentRelations = studentData.parents.map(parent => ({
        student_id: id,
        parent_id: parent.id,
        relationship: parent.relationship,
        is_primary: parent.is_primary,
      }))

      console.log('Creating new parent relations:', parentRelations)

      const { error: parentError } = await supabase
        .from('student_parent')
        .insert(parentRelations)

      if (parentError) {
        console.error('Error creating parent relationships:', parentError)
      } else {
        console.log('Created new parent relationships for student:', id)
      }
    }

    return { success: true, data: student }
  } catch (error) {
    console.error('Failed to update student with parents:', error)
    throw error
  }
}

// Legacy function for backward compatibility
export async function updateStudent(id: string, studentData: Partial<DatabaseStudent>) {
  try {
    const supabase = await createServerClient()

    const { data, error } = await supabase
      .from('students')
      .update(studentData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating student:', error)
      throw error
    }

    return { success: true, data }
  } catch (error) {
    console.error('Failed to update student:', error)
    throw error
  }
}

export async function deleteStudent(id: string) {
  try {
    const supabase = await createServerClient()
    
    // Delete the student (related records will be deleted via cascading delete)
    const { error } = await supabase
      .from('students')
      .delete()
      .eq('id', id)
    
    if (error) {
      console.error('Error deleting student:', error)
      throw error
    }
    
    return { success: true }
  } catch (error) {
    console.error('Failed to delete student:', error)
    throw error
  }
}

// ==================== CLASS-STUDENT RELATIONSHIPS ====================

export async function assignStudentToClass(studentId: string, classId: string) {
  try {
    const supabase = await createServerClient()
    
    // Check if there's an existing enrollment
    const { data: existingEnrollment, error: checkError } = await supabase
      .from('class_students')
      .select('id')
      .eq('student_id', studentId)
      .maybeSingle()
    
    if (checkError) {
      console.error('Error checking enrollment:', checkError)
      throw checkError
    }
    
    if (existingEnrollment) {
      // Update existing enrollment
      const { error } = await supabase
        .from('class_students')
        .update({ class_id: classId })
        .eq('id', existingEnrollment.id)
      
      if (error) {
        console.error('Error updating class enrollment:', error)
        throw error
      }
    } else {
      // Create new enrollment
      const { error } = await supabase
        .from('class_students')
        .insert({ student_id: studentId, class_id: classId })
      
      if (error) {
        console.error('Error creating class enrollment:', error)
        throw error
      }
    }
    
    return { success: true }
  } catch (error) {
    console.error('Failed to assign student to class:', error)
    throw error
  }
}

// ==================== PARENT-STUDENT RELATIONSHIPS ====================

export async function assignParentToStudent(
  studentId: string, 
  parentId: string, 
  relationship: string = 'parent',
  isPrimary: boolean = true
) {
  try {
    const supabase = await createServerClient()
    
    // Check if there's an existing relationship with is_primary
    const { data: existingRelation, error: checkError } = await supabase
      .from('student_parent')
      .select('id')
      .eq('student_id', studentId)
      .eq('is_primary', true)
      .maybeSingle()
    
    if (checkError) {
      console.error('Error checking parent relationship:', checkError)
      throw checkError
    }
    
    if (existingRelation && isPrimary) {
      // Update existing primary relationship
      const { error } = await supabase
        .from('student_parent')
        .update({
          parent_id: parentId,
          relationship: relationship
        })
        .eq('id', existingRelation.id)
      
      if (error) {
        console.error('Error updating parent relationship:', error)
        throw error
      }
    } else {
      // Create new relationship
      const { error } = await supabase
        .from('student_parent')
        .insert({
          student_id: studentId,
          parent_id: parentId,
          relationship: relationship,
          is_primary: isPrimary
        })
      
      if (error) {
        console.error('Error creating parent relationship:', error)
        throw error
      }
    }
    
    return { success: true }
  } catch (error) {
    console.error('Failed to assign parent to student:', error)
    throw error
  }
}

// ==================== ACHIEVEMENTS ====================

export async function getStudentAchievements(studentId: string) {
  try {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('achievements')
      .select(`
        id,
        student_id,
        subject:subjects (
          id,
          name,
          category
        ),
        value,
        grade,
        notes,
        verified_by,
        teachers:verified_by (
          id,
          name
        ),
        achievement_date,
        created_at,
        updated_at
      `)
      .eq('student_id', studentId)
      .order('achievement_date', { ascending: false })
    
    if (error) {
      console.error('Error fetching achievements:', error)
      // Return mock data as fallback
      return getMockAchievements(studentId)
    }
    
    return data.map((achievement: any) => ({
      ...achievement,
      subject_name: achievement.subject?.name || "Unknown Subject",
      teacher_name: achievement.teachers?.name || "Unknown Teacher"
    }))
  } catch (error) {
    console.error('Failed to get achievements:', error)
    return getMockAchievements(studentId)
  }
}

export async function createAchievement(achievementData: {
  student_id: string;
  subject_id: string;
  value: string;
  grade?: string;
  notes?: string;
  verified_by: string;
  achievement_date: string;
}) {
  try {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('achievements')
      .insert(achievementData)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating achievement:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Failed to create achievement:', error)
    throw error
  }
}

export async function updateAchievement(id: string, achievementData: Partial<DatabaseAchievement>) {
  try {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('achievements')
      .update(achievementData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating achievement:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Failed to update achievement:', error)
    throw error
  }
}

export async function deleteAchievement(id: string) {
  try {
    const supabase = await createServerClient()
    
    const { error } = await supabase
      .from('achievements')
      .delete()
      .eq('id', id)
    
    if (error) {
      console.error('Error deleting achievement:', error)
      throw error
    }
    
    return { success: true }
  } catch (error) {
    console.error('Failed to delete achievement:', error)
    throw error
  }
}

// ==================== QURAN PROGRESS ====================

// Re-implement Quran progress functions based on timeline_entries or another appropriate table
// This would require updating according to the new schema

// ==================== CLASSES ====================

export async function getClasses() {
  try {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('classes')
      .select(`
        id,
        class_id,
        name,
        level,
        academic_year,
        homeroom_teacher:teachers (
          id, 
          name
        )
      `)
      .order('name', { ascending: true })
    
    if (error) {
      console.error('Error fetching classes:', error)
      return []
    }
    
    return data.map((classItem: any) => ({
      ...classItem,
      teacher_name: classItem.homeroom_teacher?.name || "Unknown Teacher"
    }))
  } catch (error) {
    console.error('Failed to get classes:', error)
    return []
  }
}

// ==================== PARENTS ====================

export async function getParents(limit = 100, offset = 0, search = "") {
  try {
    const supabase = await createServerClient()
    
    let query = supabase
      .from('parents')
      .select('id, name, email, phone, address, occupation, user_id')
      .range(offset, offset + limit - 1)
      .order('name', { ascending: true })
    
    if (search) {
      query = query.ilike('name', `%${search}%`)
    }
    
    const { data, error } = await query
    
    if (error) {
      console.error('Error fetching parents:', error)
      return []
    }
    
    return data
  } catch (error) {
    console.error('Failed to get parents:', error)
    return []
  }
}

export async function getParentById(id: string) {
  try {
    const supabase = await createServerClient()
    
    // Get the parent's basic information
    const { data: parent, error } = await supabase
      .from('parents')
      .select(`
        id,
        user_id,
        name,
        email,
        phone,
        address,
        occupation
      `)
      .eq('id', id)
      .single()
    
    if (error) {
      console.error('Error fetching parent:', error)
      // Return mock data as fallback
      return {
        id: id,
        user_id: "",
        name: "Mock Parent",
        email: "<EMAIL>",
        phone: "+62812345678",
        address: "Mock Address",
        occupation: "Mock Occupation",
        children: []
      }
    }
    
    // Add children information
    const parentData = {
      ...parent,
      children: []
    } as DatabaseParent & { children: any[] };
    
    // Get children information for the parent
    const { data: childRelations, error: childrenError } = await supabase
      .from('student_parent')
      .select(`
        relationship,
        is_primary,
        student:students (
          id,
          student_id,
          name,
          gender,
          birth_date,
          address,
          photo_url,
          batch,
          status
        )
      `)
      .eq('parent_id', parent.id);
    
    if (!childrenError && childRelations) {
      parentData.children = childRelations.map((relation: any) => ({
        ...relation.student,
        relationship: relation.relationship,
        is_primary: relation.is_primary
      }));
    }
    
    return parentData;
  } catch (error) {
    console.error('Failed to get parent by ID:', error)
    return {
      id: id,
      profile_id: null,
      name: "Mock Parent",
      email: "<EMAIL>",
      phone: "+62812345678",
      address: "Mock Address",
      occupation: "Mock Occupation",
      children: []
    }
  }
}

export async function getParentByUserId(userId: string) {
  try {
    const supabase = await createServerClient()
    
    // Get the parent's basic information
    const { data: parent, error } = await supabase
      .from('parents')
      .select(`
        id,
        profile_id,
        name,
        email,
        phone, 
        address,
        occupation
      `)
      .eq('profile_id', userId)
      .single()
    
    if (error) {
      console.error('Error fetching parent by user ID:', error)
      return null
    }
    
    // Add children information
    const parentData = {
      ...parent,
      children: []
    } as DatabaseParent & { children: any[] };
    
    // Get children information for the parent
    const { data: childRelations, error: childrenError } = await supabase
      .from('student_parent')
      .select(`
        relationship,
        is_primary,
        student:students (
          id,
          student_id, 
          name,
          gender,
          birth_date,
          address,
          photo_url,
          batch,
          status
        )
      `)
      .eq('parent_id', parent.id);
    
    if (!childrenError && childRelations) {
      parentData.children = childRelations.map((relation: any) => ({
        ...relation.student,
        relationship: relation.relationship,
        is_primary: relation.is_primary
      }));
    }
    
    return parentData;
  } catch (error) {
    console.error('Failed to get parent by user ID:', error)
    return null
  }
}

export async function createParent(parentData: {
  name: string;
  email: string;
  phone: string;
  address: string;
  occupation?: string;
  user_id: string;
}) {
  try {
    const supabase = await createServerClient()

    // First, try to create with the provided user_id
    let { data, error } = await supabase
      .from('parents')
      .insert(parentData)
      .select()
      .single()

    // If foreign key constraint error, try with a different approach
    if (error && error.code === '23503') {
      console.log('Foreign key constraint error, trying alternative approach...')

      // Try to find an existing user or use a default approach
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user?.id) {
        // Use current session user ID
        const modifiedData = { ...parentData, user_id: session.user.id }
        const { data: retryData, error: retryError } = await supabase
          .from('parents')
          .insert(modifiedData)
          .select()
          .single()

        if (retryError) {
          console.error('Retry failed:', retryError)
          throw retryError
        }

        data = retryData
      } else {
        throw error
      }
    } else if (error) {
      console.error('Error creating parent:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Failed to create parent:', error)
    throw error
  }
}

export async function updateParent(id: string, parentData: Partial<DatabaseParent>) {
  try {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('parents')
      .update(parentData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating parent:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Failed to update parent:', error)
    throw error
  }
}

export async function deleteParent(id: string) {
  try {
    const supabase = await createServerClient()
    
    const { error } = await supabase
      .from('parents')
      .delete()
      .eq('id', id)
    
    if (error) {
      console.error('Error deleting parent:', error)
      throw error
    }
    
    return { success: true }
  } catch (error) {
    console.error('Failed to delete parent:', error)
    throw error
  }
}

// ==================== HELPER FUNCTIONS ====================

function transformStudentData(student: DatabaseStudent, classInfo?: any, parentInfo?: any, relationship?: string) {
  // Use type assertion to handle the case where some properties might be missing
  const studentWithOptionalFields = student as Partial<DatabaseStudent> & Pick<DatabaseStudent, 'id' | 'student_id' | 'name'>;
  
  return {
    id: studentWithOptionalFields.id,
    student_id: studentWithOptionalFields.student_id,
    name: studentWithOptionalFields.name,
    photo: studentWithOptionalFields.photo_url || "/placeholder.svg?height=320&width=240", // Use photo_url or default
    batch: studentWithOptionalFields.batch || classInfo?.name || "Unknown Batch",
    className: classInfo?.name || "Unknown Class",
    classLevel: classInfo?.level || "Unknown Level",
    academicYear: classInfo?.academic_year || "Unknown Year",
    class_id: classInfo?.id,
    parentName: parentInfo?.name || "Unknown Parent",
    parentEmail: parentInfo?.email,
    parentPhone: parentInfo?.phone,
    parent_id: parentInfo?.id,
    relationship: relationship || "parent",
    birth_date: studentWithOptionalFields.birth_date,
    address: studentWithOptionalFields.address,
    gender: studentWithOptionalFields.gender,
    status: studentWithOptionalFields.status,
    profile_id: null, // Students table doesn't have profile_id
    // Mock achievements structure until we load real achievements data
    achievements: {
      alQuran: {
        value: "Belum diisi",
        grade: "Belum diisi"
      },
      haditsArbain: {
        value: "Belum diisi",
        grade: "Belum diisi"
      },
      ushulTsalatsah: {
        value: "Belum diisi",
        grade: "Belum diisi"
      },
      ghoyahWaTaqrib: {
        value: "Belum diisi",
        grade: "Belum diisi"
      },
      alAjurumiyyah: {
        value: "Belum diisi",
        grade: "Belum diisi"
      },
      kitabGundul: {
        value: "Belum diisi",
        grade: "Belum diisi"
      },
      publicSpeaking: {
        value: "Belum diisi",
        grade: "Belum diisi"
      }
    }
  }
}

// Enhanced transformation function that includes real achievements and timeline data
function transformStudentDataWithAchievements(
  student: any,
  classInfo?: any,
  parentInfo?: any,
  achievements: any[] = [],
  timelineDetails: any[] = []
) {
  // Map achievements and timeline details to the expected structure
  const computedAchievements = computeAchievementsFromData(achievements, timelineDetails);

  return {
    id: student.id,
    student_id: student.student_id,
    name: student.name,
    gender: student.gender,
    birth_date: student.birth_date,
    address: student.address,
    photo: student.photo_url || "/placeholder.svg?height=320&width=240",
    batch: student.batch || classInfo?.name || "Unknown Batch",
    status: student.status,
    profile_id: null, // Students table doesn't have profile_id
    created_at: student.created_at,
    updated_at: student.updated_at,
    // Class information
    class: classInfo ? {
      id: classInfo.id,
      name: classInfo.name,
      level: classInfo.level
    } : undefined,
    className: classInfo?.name || "Unknown Class",
    classLevel: classInfo?.level || "Unknown Level",
    academicYear: classInfo?.academic_year || "Unknown Year",
    class_id: classInfo?.id,
    // Parent information
    parentName: parentInfo?.name || "Unknown Parent",
    parentEmail: parentInfo?.email,
    parentPhone: parentInfo?.phone,
    parent_id: parentInfo?.id,
    // Raw achievements and timeline data
    achievements: achievements,
    timelineDetails: timelineDetails,
    // Computed achievements for backward compatibility
    computedAchievements: computedAchievements
  }
}

// Helper function to compute achievements from database data
function computeAchievementsFromData(achievements: any[], timelineDetails: any[]) {
  const subjectMap: { [key: string]: { value: string, grade: string } } = {
    alQuran: { value: "Belum diisi", grade: "Belum diisi" },
    haditsArbain: { value: "Belum diisi", grade: "Belum diisi" },
    ushulTsalatsah: { value: "Belum diisi", grade: "Belum diisi" },
    ghoyahWaTaqrib: { value: "Belum diisi", grade: "Belum diisi" },
    alAjurumiyyah: { value: "Belum diisi", grade: "Belum diisi" },
    kitabGundul: { value: "Belum diisi", grade: "Belum diisi" },
    publicSpeaking: { value: "Belum diisi", grade: "Belum diisi" }
  };

  // Map subject names to our expected keys
  const subjectNameMap: { [key: string]: string } = {
    'Al-Quran': 'alQuran',
    'Tahfidz Al-Quran': 'alQuran',
    'Hadits': 'haditsArbain',
    'Hadits Arbain': 'haditsArbain',
    'Ushul Tsalatsah': 'ushulTsalatsah',
    'Ghoyah wa Taqrib': 'ghoyahWaTaqrib',
    'Al-Ajurumiyyah': 'alAjurumiyyah',
    'Kitab Gundul': 'kitabGundul',
    'Public Speaking': 'publicSpeaking',
    'Bahasa Arab': 'alAjurumiyyah',
    'Fiqih': 'ghoyahWaTaqrib',
    'Akhlak': 'ushulTsalatsah'
  };

  // Process achievements
  achievements.forEach(achievement => {
    const subjectName = achievement.subject?.name;
    if (subjectName && subjectNameMap[subjectName]) {
      const key = subjectNameMap[subjectName];
      subjectMap[key] = {
        value: achievement.value || "Belum diisi",
        grade: achievement.grade || "Belum diisi"
      };
    }
  });

  // Process timeline details (more recent data)
  timelineDetails.forEach(detail => {
    const subjectName = detail.subject?.name;
    if (subjectName && subjectNameMap[subjectName]) {
      const key = subjectNameMap[subjectName];
      subjectMap[key] = {
        value: detail.value || "Belum diisi",
        grade: detail.grade || "Belum diisi"
      };
    }
  });

  return subjectMap;
}

function getMockStudents() {
  // Return minimal mock data when database is unavailable
  return [
    {
      id: "mock-1",
      student_id: "STD-001",
      name: "Ahmad Fauzi",
      gender: "male",
      birth_date: "2010-01-15",
      address: "Jl. Contoh No. 123",
      photo: "/placeholder.svg?height=320&width=240",
      batch: "Angkatan 2024",
      status: "active",
      profile_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      className: "Kelas 7A",
      classLevel: "SMP",
      academicYear: "2024-2025",
      computedAchievements: {
        alQuran: { value: "Belum diisi", grade: "Belum diisi" },
        haditsArbain: { value: "Belum diisi", grade: "Belum diisi" },
        ushulTsalatsah: { value: "Belum diisi", grade: "Belum diisi" },
        ghoyahWaTaqrib: { value: "Belum diisi", grade: "Belum diisi" },
        alAjurumiyyah: { value: "Belum diisi", grade: "Belum diisi" },
        kitabGundul: { value: "Belum diisi", grade: "Belum diisi" },
        publicSpeaking: { value: "Belum diisi", grade: "Belum diisi" }
      }
    },
    {
      id: "mock-2",
      student_id: "STD-002",
      name: "Siti Aisyah",
      gender: "female",
      birth_date: "2010-03-20",
      address: "Jl. Contoh No. 456",
      photo: "/placeholder.svg?height=320&width=240",
      batch: "Angkatan 2024",
      status: "active",
      profile_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      className: "Kelas 7B",
      classLevel: "SMP",
      academicYear: "2024-2025",
      computedAchievements: {
        alQuran: { value: "Belum diisi", grade: "Belum diisi" },
        haditsArbain: { value: "Belum diisi", grade: "Belum diisi" },
        ushulTsalatsah: { value: "Belum diisi", grade: "Belum diisi" },
        ghoyahWaTaqrib: { value: "Belum diisi", grade: "Belum diisi" },
        alAjurumiyyah: { value: "Belum diisi", grade: "Belum diisi" },
        kitabGundul: { value: "Belum diisi", grade: "Belum diisi" },
        publicSpeaking: { value: "Belum diisi", grade: "Belum diisi" }
      }
    }
  ]
}

function getMockStudentById(id: string) {
  const mockStudents = getMockStudents()
  const mockStudent = mockStudents.find(s => s.id === id)
  if (!mockStudent) {
    // Return a default mock student
    return {
      id: id,
      student_id: "STD-MOCK",
      name: "Mock Student",
      gender: "male",
      birth_date: "2010-01-01",
      address: "Jl. Contoh No. 123, Ungaran",
      photo: "/placeholder.svg?height=320&width=240",
      batch: "Angkatan Mock",
      status: "active",
      profile_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      className: "Mock Class",
      classLevel: "SMP",
      academicYear: "2024-2025",
      parentName: "Mock Parent",
      parentEmail: "<EMAIL>",
      parentPhone: "+6281234567890",
      computedAchievements: {
        alQuran: { value: "Belum diisi", grade: "Belum diisi" },
        haditsArbain: { value: "Belum diisi", grade: "Belum diisi" },
        ushulTsalatsah: { value: "Belum diisi", grade: "Belum diisi" },
        ghoyahWaTaqrib: { value: "Belum diisi", grade: "Belum diisi" },
        alAjurumiyyah: { value: "Belum diisi", grade: "Belum diisi" },
        kitabGundul: { value: "Belum diisi", grade: "Belum diisi" },
        publicSpeaking: { value: "Belum diisi", grade: "Belum diisi" }
      }
    }
  }
  return mockStudent
}

function getMockAchievements(studentId: string) {
  // Return empty array for mock achievements
  return []
}