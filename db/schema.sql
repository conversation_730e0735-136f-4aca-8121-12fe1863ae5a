-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table linked to auth.users
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'teacher', 'parent')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create students table
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female')),
    birth_date DATE,
    address TEXT,
    photo_url TEXT,
    batch VARCHAR(50),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'graduated', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create parents table
CREATE TABLE parents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID REFERENCES profiles(id),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    address TEXT,
    occupation VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create student-parent relationship table
CREATE TABLE student_parent (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    parent_id UUID NOT NULL REFERENCES parents(id) ON DELETE CASCADE,
    relationship VARCHAR(50) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, parent_id)
);

-- Create teachers table
CREATE TABLE teachers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID REFERENCES profiles(id),
    teacher_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    specialization VARCHAR(255),
    photo_url TEXT,
    join_date DATE,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create classes table
CREATE TABLE classes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    class_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    level VARCHAR(50) NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    homeroom_teacher_id UUID REFERENCES teachers(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create class-student relationship table
CREATE TABLE class_students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(class_id, student_id)
);

-- Create subjects table
CREATE TABLE subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create achievements table
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    subject_id UUID NOT NULL REFERENCES subjects(id),
    value TEXT NOT NULL,
    grade VARCHAR(50),
    notes TEXT,
    verified_by UUID REFERENCES teachers(id),
    achievement_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create timeline entries table
CREATE TABLE timeline_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    teacher_id UUID NOT NULL REFERENCES teachers(id),
    month VARCHAR(20) NOT NULL,
    year INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, month, year)
);

-- Create timeline details table
CREATE TABLE timeline_details (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timeline_id UUID NOT NULL REFERENCES timeline_entries(id) ON DELETE CASCADE,
    subject_id UUID NOT NULL REFERENCES subjects(id),
    value TEXT NOT NULL,
    grade VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create timeline activities table
CREATE TABLE timeline_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timeline_id UUID NOT NULL REFERENCES timeline_entries(id) ON DELETE CASCADE,
    activity TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create attendance table
CREATE TABLE attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    class_id UUID NOT NULL REFERENCES classes(id),
    attendance_date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('present', 'absent', 'late', 'excused')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, attendance_date)
);

-- Create behavior records table
CREATE TABLE behavior_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    teacher_id UUID NOT NULL REFERENCES teachers(id),
    month VARCHAR(20) NOT NULL,
    year INTEGER NOT NULL,
    behavior_grade VARCHAR(50) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, month, year)
);

-- Create PPDB applications table
CREATE TABLE ppdb_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_id VARCHAR(50) UNIQUE NOT NULL,
    student_name VARCHAR(255) NOT NULL,
    parent_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20) NOT NULL,
    address TEXT,
    birth_date DATE,
    previous_school VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'waitlisted')),
    documents_url TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create blog posts table
CREATE TABLE blog_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    author_id UUID NOT NULL REFERENCES profiles(id),
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    featured_image TEXT,
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create blog categories table
CREATE TABLE blog_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create post-categories relationship table
CREATE TABLE post_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    post_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES blog_categories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(post_id, category_id)
);

-- Create website images table for uploaded images
CREATE TABLE website_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text TEXT,
    caption TEXT,
    category VARCHAR(100) DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_students_profile_id ON students(profile_id);
CREATE INDEX idx_parents_profile_id ON parents(profile_id);
CREATE INDEX idx_teachers_profile_id ON teachers(profile_id);
CREATE INDEX idx_student_parent_student_id ON student_parent(student_id);
CREATE INDEX idx_student_parent_parent_id ON student_parent(parent_id);
CREATE INDEX idx_class_students_class_id ON class_students(class_id);
CREATE INDEX idx_class_students_student_id ON class_students(student_id);
CREATE INDEX idx_achievements_student_id ON achievements(student_id);
CREATE INDEX idx_achievements_subject_id ON achievements(subject_id);
CREATE INDEX idx_timeline_entries_student_id ON timeline_entries(student_id);
CREATE INDEX idx_timeline_entries_teacher_id ON timeline_entries(teacher_id);
CREATE INDEX idx_timeline_details_timeline_id ON timeline_details(timeline_id);
CREATE INDEX idx_timeline_details_subject_id ON timeline_details(subject_id);
CREATE INDEX idx_timeline_activities_timeline_id ON timeline_activities(timeline_id);
CREATE INDEX idx_attendance_student_id ON attendance(student_id);
CREATE INDEX idx_attendance_class_id ON attendance(class_id);
CREATE INDEX idx_attendance_date ON attendance(attendance_date);
CREATE INDEX idx_behavior_records_student_id ON behavior_records(student_id);
CREATE INDEX idx_behavior_records_teacher_id ON behavior_records(teacher_id);
CREATE INDEX idx_blog_posts_author_id ON blog_posts(author_id);
CREATE INDEX idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX idx_blog_posts_status ON blog_posts(status);
CREATE INDEX idx_post_categories_post_id ON post_categories(post_id);
CREATE INDEX idx_post_categories_category_id ON post_categories(category_id);
CREATE INDEX idx_website_images_key ON website_images(key);
CREATE INDEX idx_website_images_category ON website_images(category);

-- Create functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_profiles_modtime
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_students_modtime
    BEFORE UPDATE ON students
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_parents_modtime
    BEFORE UPDATE ON parents
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_teachers_modtime
    BEFORE UPDATE ON teachers
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_classes_modtime
    BEFORE UPDATE ON classes
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_subjects_modtime
    BEFORE UPDATE ON subjects
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_achievements_modtime
    BEFORE UPDATE ON achievements
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_timeline_entries_modtime
    BEFORE UPDATE ON timeline_entries
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_timeline_details_modtime
    BEFORE UPDATE ON timeline_details
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_attendance_modtime
    BEFORE UPDATE ON attendance
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_behavior_records_modtime
    BEFORE UPDATE ON behavior_records
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_ppdb_applications_modtime
    BEFORE UPDATE ON ppdb_applications
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_blog_posts_modtime
    BEFORE UPDATE ON blog_posts
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_blog_categories_modtime
    BEFORE UPDATE ON blog_categories
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_website_images_modtime
    BEFORE UPDATE ON website_images
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

-- Set up Row Level Security (RLS)
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE parents ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_parent ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE class_students ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE behavior_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE ppdb_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE website_images ENABLE ROW LEVEL SECURITY;

-- Create policies for each table
-- Profiles table policies
CREATE POLICY profiles_admin_all ON profiles
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY profiles_self_read ON profiles
    FOR SELECT TO authenticated
    USING (auth.uid() = id);

-- Students table policies
CREATE POLICY students_admin_all ON students
    FOR ALL TO authenticated 
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY students_teacher_read ON students
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY students_parent_read ON students
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id 
            FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            WHERE sp.student_id = students.id
        )
    );

-- Parents table policies
CREATE POLICY parents_admin_all ON parents
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY parents_self_read ON parents
    FOR SELECT TO authenticated
    USING (auth.uid() = profile_id);

CREATE POLICY parents_self_update ON parents
    FOR UPDATE TO authenticated
    USING (auth.uid() = profile_id);

-- Teachers table policies
CREATE POLICY teachers_admin_all ON teachers
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY teachers_self_read ON teachers
    FOR SELECT TO authenticated
    USING (auth.uid() = profile_id);

CREATE POLICY teachers_self_update ON teachers
    FOR UPDATE TO authenticated
    USING (auth.uid() = profile_id);

-- Student-Parent relationship policies
CREATE POLICY student_parent_admin_all ON student_parent
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY student_parent_parent_read ON student_parent
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id FROM parents p WHERE p.id = parent_id
        )
    );

-- Classes table policies
CREATE POLICY classes_admin_all ON classes
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY classes_teacher_read ON classes
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (SELECT profile_id FROM teachers WHERE id = homeroom_teacher_id)
    );

-- Class-Students table policies
CREATE POLICY class_students_admin_all ON class_students
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY class_students_teacher_read ON class_students
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (SELECT profile_id FROM teachers)
    );

-- Subjects table policies
CREATE POLICY subjects_admin_all ON subjects
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY subjects_all_read ON subjects
    FOR SELECT TO authenticated
    USING (true);

-- Achievements table policies
CREATE POLICY achievements_admin_all ON achievements
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY achievements_teacher_read ON achievements
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY achievements_teacher_insert ON achievements
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY achievements_teacher_update ON achievements
    FOR UPDATE TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY achievements_parent_read ON achievements
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            WHERE sp.student_id = achievements.student_id
        )
    );

-- Timeline entries table policies
CREATE POLICY timeline_entries_admin_all ON timeline_entries
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY timeline_entries_teacher_read ON timeline_entries
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_entries_teacher_insert ON timeline_entries
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_entries_teacher_update ON timeline_entries
    FOR UPDATE TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_entries_parent_read ON timeline_entries
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            WHERE sp.student_id = timeline_entries.student_id
        )
    );

-- Timeline details table policies
CREATE POLICY timeline_details_admin_all ON timeline_details
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY timeline_details_teacher_read ON timeline_details
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_details_teacher_insert ON timeline_details
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_details_teacher_update ON timeline_details
    FOR UPDATE TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_details_parent_read ON timeline_details
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            JOIN timeline_entries te ON sp.student_id = te.student_id
            WHERE timeline_details.timeline_id = te.id
        )
    );

-- Timeline activities table policies
CREATE POLICY timeline_activities_admin_all ON timeline_activities
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY timeline_activities_teacher_read ON timeline_activities
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_activities_teacher_insert ON timeline_activities
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY timeline_activities_teacher_update ON timeline_activities
    FOR UPDATE TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

-- Attendance table policies
CREATE POLICY attendance_admin_all ON attendance
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY attendance_teacher_read ON attendance
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY attendance_teacher_insert ON attendance
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY attendance_teacher_update ON attendance
    FOR UPDATE TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY attendance_parent_read ON attendance
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            WHERE sp.student_id = attendance.student_id
        )
    );

-- Behavior records table policies
CREATE POLICY behavior_records_admin_all ON behavior_records
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY behavior_records_teacher_read ON behavior_records
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY behavior_records_teacher_insert ON behavior_records
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY behavior_records_teacher_update ON behavior_records
    FOR UPDATE TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY behavior_records_parent_read ON behavior_records
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            WHERE sp.student_id = behavior_records.student_id
        )
    );

-- PPDB applications table policies
CREATE POLICY ppdb_applications_admin_all ON ppdb_applications
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY ppdb_applications_self_read ON ppdb_applications
    FOR SELECT TO authenticated
    USING (auth.uid() = processed_by);

CREATE POLICY ppdb_applications_self_insert ON ppdb_applications
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = processed_by);

CREATE POLICY ppdb_applications_self_update ON ppdb_applications
    FOR UPDATE TO authenticated
    USING (auth.uid() = processed_by);

-- Blog posts table policies
CREATE POLICY blog_posts_admin_all ON blog_posts
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY blog_posts_author_read ON blog_posts
    FOR SELECT TO authenticated
    USING (auth.uid() = author_id);

CREATE POLICY blog_posts_author_insert ON blog_posts
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = author_id);

CREATE POLICY blog_posts_author_update ON blog_posts
    FOR UPDATE TO authenticated
    USING (auth.uid() = author_id);

CREATE POLICY blog_posts_all_read ON blog_posts
    FOR SELECT TO authenticated
    USING (status = 'published');

-- Blog categories table policies
CREATE POLICY blog_categories_admin_all ON blog_categories
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY blog_categories_all_read ON blog_categories
    FOR SELECT TO authenticated
    USING (true);

-- Post-categories relationship table policies
CREATE POLICY post_categories_admin_all ON post_categories
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY post_categories_all_read ON post_categories
    FOR SELECT TO authenticated
    USING (true);

-- Insert sample data for testing
-- Insert sample subjects
INSERT INTO subjects (name, category, description) VALUES
('Al-Quran', 'Tahfidz', 'Hafalan dan tajwid Al-Quran'),
('Hadits', 'Tahfidz', 'Hafalan hadits-hadits pilihan'),
('Fiqih', 'Akademik', 'Pembelajaran tentang hukum Islam'),
('Akhlak', 'Adab', 'Pembelajaran tentang adab dan akhlak Islami'),
('Bahasa Arab', 'Akademik', 'Pembelajaran bahasa Arab');

-- Insert sample profiles
INSERT INTO profiles (id, email, phone, name, role) VALUES
('11111111-1111-1111-1111-111111111111', '<EMAIL>', '6281234567890', 'Administrator', 'admin'),
('*************-2222-2222-************', '<EMAIL>', '6281234567891', 'Ustadz Ahmad', 'teacher'),
('*************-3333-3333-************', '<EMAIL>', '6281234567892', 'Ustadz Mahmud', 'teacher'),
('*************-4444-4444-************', '<EMAIL>', '6281234567893', 'Budi Santoso', 'parent'),
('55555555-5555-5555-5555-555555555555', '<EMAIL>', '6281234567894', 'Siti Rahmah', 'parent'),
('66666666-6666-6666-6666-666666666666', '<EMAIL>', '6281234567895', 'Ahmad Fauzi', 'parent');

-- Insert sample teachers
INSERT INTO teachers (id, profile_id, teacher_id, name, specialization, photo_url, join_date, status) VALUES
('11111111-aaaa-bbbb-cccc-000000000001', '*************-2222-2222-************', 'TCH-001', 'Ustadz Ahmad', 'Tahfidz Al-Quran', '/photos/teachers/ahmad.jpg', '2020-07-15', 'active'),
('11111111-aaaa-bbbb-cccc-000000000002', '*************-3333-3333-************', 'TCH-002', 'Ustadz Mahmud', 'Fiqih', '/photos/teachers/mahmud.jpg', '2021-01-10', 'active');

-- Insert sample classes
INSERT INTO classes (id, class_id, name, level, academic_year, homeroom_teacher_id) VALUES
('11111111-aaaa-bbbb-cccc-000000000003', 'CLS-SMP7-A', 'Kelas 7A', 'SMP', '2023-2024', '11111111-aaaa-bbbb-cccc-000000000001'),
('11111111-aaaa-bbbb-cccc-000000000004', 'CLS-SMP8-A', 'Kelas 8A', 'SMP', '2023-2024', '11111111-aaaa-bbbb-cccc-000000000002'),
('11111111-aaaa-bbbb-cccc-000000000005', 'CLS-SMK10-A', 'Kelas 10A', 'SMK', '2023-2024', '11111111-aaaa-bbbb-cccc-000000000001');

-- Insert sample parents
INSERT INTO parents (id, profile_id, name, phone, email, address, occupation) VALUES
('11111111-aaaa-bbbb-cccc-000000000006', '*************-4444-4444-************', 'Budi Santoso', '6281234567893', '<EMAIL>', 'Jl. Merdeka No. 123, Semarang', 'Pengusaha'),
('11111111-aaaa-bbbb-cccc-000000000007', '55555555-5555-5555-5555-555555555555', 'Siti Rahmah', '6281234567894', '<EMAIL>', 'Jl. Pahlawan No. 45, Ungaran', 'Guru'),
('11111111-aaaa-bbbb-cccc-000000000008', '66666666-6666-6666-6666-666666666666', 'Ahmad Fauzi', '6281234567895', '<EMAIL>', 'Jl. Diponegoro No. 78, Salatiga', 'Dokter');

-- Insert sample students
INSERT INTO students (id, profile_id, student_id, name, gender, birth_date, address, photo_url, batch, status) VALUES
('11111111-aaaa-bbbb-cccc-000000000009', NULL, 'STD-001', 'Abdullah Farhan', 'male', '2010-05-15', 'Jl. Merdeka No. 123, Semarang', '/photos/students/abdullah.jpg', 'Angkatan 2023', 'active'),
('11111111-aaaa-bbbb-cccc-000000000010', NULL, 'STD-002', 'Aisyah Putri', 'female', '2009-03-20', 'Jl. Pahlawan No. 45, Ungaran', '/photos/students/aisyah.jpg', 'Angkatan 2022', 'active'),
('11111111-aaaa-bbbb-cccc-000000000011', NULL, 'STD-003', 'Muhammad Rizki', 'male', '2011-12-10', 'Jl. Diponegoro No. 78, Salatiga', '/photos/students/rizki.jpg', 'Angkatan 2023', 'active'),
('11111111-aaaa-bbbb-cccc-000000000012', NULL, 'STD-004', 'Zahra Salsabila', 'female', '2008-08-25', 'Jl. Sudirman No. 90, Semarang', '/photos/students/zahra.jpg', 'Angkatan 2022', 'active'),
('11111111-aaaa-bbbb-cccc-000000000013', NULL, 'STD-005', 'Ahmad Zaki', 'male', '2010-01-30', 'Jl. Kyai H. No. 12, Ungaran', '/photos/students/zaki.jpg', 'Angkatan 2023', 'active');

-- Insert student-parent relationships
INSERT INTO student_parent (student_id, parent_id, relationship, is_primary) VALUES
('11111111-aaaa-bbbb-cccc-000000000009', '11111111-aaaa-bbbb-cccc-000000000006', 'father', true),
('11111111-aaaa-bbbb-cccc-000000000010', '11111111-aaaa-bbbb-cccc-000000000007', 'mother', true),
('11111111-aaaa-bbbb-cccc-000000000011', '11111111-aaaa-bbbb-cccc-000000000008', 'father', true),
('11111111-aaaa-bbbb-cccc-000000000012', '11111111-aaaa-bbbb-cccc-000000000007', 'mother', true),
('11111111-aaaa-bbbb-cccc-000000000013', '11111111-aaaa-bbbb-cccc-000000000006', 'father', true);

-- Insert class-student relationships
INSERT INTO class_students (class_id, student_id) VALUES
('11111111-aaaa-bbbb-cccc-000000000003', '11111111-aaaa-bbbb-cccc-000000000009'),
('11111111-aaaa-bbbb-cccc-000000000003', '11111111-aaaa-bbbb-cccc-000000000011'),
('11111111-aaaa-bbbb-cccc-000000000003', '11111111-aaaa-bbbb-cccc-000000000013'),
('11111111-aaaa-bbbb-cccc-000000000004', '11111111-aaaa-bbbb-cccc-000000000010'),
('11111111-aaaa-bbbb-cccc-000000000004', '11111111-aaaa-bbbb-cccc-000000000012');

-- Insert sample achievements
INSERT INTO achievements (student_id, subject_id, value, grade, notes, verified_by, achievement_date) VALUES
('11111111-aaaa-bbbb-cccc-000000000009', 
 (SELECT id FROM subjects WHERE name = 'Al-Quran' LIMIT 1), 
 'Hafal Juz 30', 'A', 'Sangat lancar dalam hafalan', 
 '11111111-aaaa-bbbb-cccc-000000000001', 
 '2023-12-15'),
 
('11111111-aaaa-bbbb-cccc-000000000010', 
 (SELECT id FROM subjects WHERE name = 'Hadits' LIMIT 1), 
 'Hafal 20 hadits', 'B+', 'Memahami dengan baik', 
 '11111111-aaaa-bbbb-cccc-000000000002', 
 '2023-11-20'),
 
('11111111-aaaa-bbbb-cccc-000000000011', 
 (SELECT id FROM subjects WHERE name = 'Bahasa Arab' LIMIT 1), 
 'Menguasai dasar-dasar nahwu', 'A-', 'Kemampuan bicara sangat baik', 
 '11111111-aaaa-bbbb-cccc-000000000002', 
 '2023-12-05');

-- Insert sample timeline entries
INSERT INTO timeline_entries (student_id, teacher_id, month, year) VALUES
('11111111-aaaa-bbbb-cccc-000000000009', '11111111-aaaa-bbbb-cccc-000000000001', 'December', 2023),
('11111111-aaaa-bbbb-cccc-000000000010', '11111111-aaaa-bbbb-cccc-000000000002', 'December', 2023);

-- Insert sample timeline details
INSERT INTO timeline_details (timeline_id, subject_id, value, grade, notes) VALUES
((SELECT id FROM timeline_entries WHERE student_id = '11111111-aaaa-bbbb-cccc-000000000009' AND month = 'December' LIMIT 1),
 (SELECT id FROM subjects WHERE name = 'Al-Quran' LIMIT 1),
 'Sedang menghafal Juz 29', 'A-', 'Progres sangat baik'),
 
((SELECT id FROM timeline_entries WHERE student_id = '11111111-aaaa-bbbb-cccc-000000000010' AND month = 'December' LIMIT 1),
 (SELECT id FROM subjects WHERE name = 'Fiqih' LIMIT 1),
 'Memahami bab thaharah', 'B+', 'Perlu lebih banyak latihan');

-- Insert sample attendance records
INSERT INTO attendance (student_id, class_id, attendance_date, status, notes) VALUES
('11111111-aaaa-bbbb-cccc-000000000009', '11111111-aaaa-bbbb-cccc-000000000003', '2023-12-01', 'present', NULL),
('11111111-aaaa-bbbb-cccc-000000000009', '11111111-aaaa-bbbb-cccc-000000000003', '2023-12-02', 'present', NULL),
('11111111-aaaa-bbbb-cccc-000000000009', '11111111-aaaa-bbbb-cccc-000000000003', '2023-12-03', 'absent', 'Sakit'),
('11111111-aaaa-bbbb-cccc-000000000010', '11111111-aaaa-bbbb-cccc-000000000004', '2023-12-01', 'present', NULL),
('11111111-aaaa-bbbb-cccc-000000000010', '11111111-aaaa-bbbb-cccc-000000000004', '2023-12-02', 'late', 'Terlambat 15 menit');

-- Insert sample behavior records
INSERT INTO behavior_records (student_id, teacher_id, month, year, behavior_grade, notes) VALUES
('11111111-aaaa-bbbb-cccc-000000000009', '11111111-aaaa-bbbb-cccc-000000000001', 'December', 2023, 'A', 'Berperilaku sangat baik, rajin membantu teman'),
('11111111-aaaa-bbbb-cccc-000000000010', '11111111-aaaa-bbbb-cccc-000000000002', 'December', 2023, 'B+', 'Sopan, namun terkadang kurang disiplin'); 