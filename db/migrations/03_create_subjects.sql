-- Create subjects table
CREATE TABLE subjects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  category VARCHAR(100) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create subject objectives table
CREATE TABLE subject_objectives (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subject_id UUID NOT NULL REFERENCES subjects(id) ON DELETE CASCADE,
  objective TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create junction table for teachers and subjects
CREATE TABLE subject_teachers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subject_id UUID NOT NULL REFERENCES subjects(id) ON DELETE CASCADE,
  teacher_id UUID NOT NULL REFERENCES teachers(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(subject_id, teacher_id)
);

-- Create indexes for better performance
CREATE INDEX idx_subject_objectives_subject_id ON subject_objectives(subject_id);
CREATE INDEX idx_subject_teachers_subject_id ON subject_teachers(subject_id);
CREATE INDEX idx_subject_teachers_teacher_id ON subject_teachers(teacher_id);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_subjects_modtime
  BEFORE UPDATE ON subjects
  FOR EACH ROW
  EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_subject_objectives_modtime
  BEFORE UPDATE ON subject_objectives
  FOR EACH ROW
  EXECUTE PROCEDURE update_modified_column();

-- Set up Row Level Security (RLS)
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE subject_objectives ENABLE ROW LEVEL SECURITY;
ALTER TABLE subject_teachers ENABLE ROW LEVEL SECURITY;

-- Admin policies (full access)
CREATE POLICY subjects_admin_all ON subjects
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY subject_objectives_admin_all ON subject_objectives
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY subject_teachers_admin_all ON subject_teachers
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

-- Teacher policies (read access)
CREATE POLICY subjects_teacher_read ON subjects
  FOR SELECT TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY subject_objectives_teacher_read ON subject_objectives
  FOR SELECT TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY subject_teachers_teacher_read ON subject_teachers
  FOR SELECT TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers));

-- Public read policies for subjects
CREATE POLICY subjects_all_read ON subjects
  FOR SELECT TO authenticated
  USING (true);

-- Sample data for subjects
INSERT INTO subjects (name, category, description, is_active) VALUES
('Al-Quran', 'Keagamaan', 'Pembelajaran Al-Quran dan tajwid', true),
('Hadits Arbain', 'Keagamaan', 'Pembelajaran 40 hadits pilihan', true),
('Bahasa Arab', 'Bahasa', 'Dasar-dasar bahasa Arab untuk pemula', true),
('Fiqih Ibadah', 'Keagamaan', 'Pembelajaran tata cara ibadah sehari-hari', true),
('Akhlak', 'Keagamaan', 'Pembentukan karakter dan akhlak mulia', true);

-- Sample objectives
WITH subject_ids AS (
  SELECT id FROM subjects WHERE name = 'Al-Quran' LIMIT 1
)
INSERT INTO subject_objectives (subject_id, objective)
SELECT id, 'Santri mampu membaca Al-Quran dengan tajwid yang benar'
FROM subject_ids
UNION ALL
SELECT id, 'Santri memahami hukum-hukum bacaan dalam Al-Quran'
FROM subject_ids
UNION ALL
SELECT id, 'Santri dapat menghafal juz 30 dengan baik'
FROM subject_ids;

WITH subject_ids AS (
  SELECT id FROM subjects WHERE name = 'Hadits Arbain' LIMIT 1
)
INSERT INTO subject_objectives (subject_id, objective)
SELECT id, 'Santri mampu menghafal minimal 20 hadits pilihan'
FROM subject_ids
UNION ALL
SELECT id, 'Santri memahami makna dari hadits-hadits yang dihafal'
FROM subject_ids;

-- Sample teacher-subject relationships 
-- (assuming we have teachers data from previous migrations)
WITH teacher_ids AS (
  SELECT id FROM teachers LIMIT 2
),
subject_ids AS (
  SELECT id FROM subjects LIMIT 3
)
INSERT INTO subject_teachers (subject_id, teacher_id)
SELECT s.id, t.id
FROM subject_ids s, teacher_ids t
ON CONFLICT DO NOTHING; 